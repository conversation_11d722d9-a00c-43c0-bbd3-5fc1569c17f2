//
//  PracticeWidget.swift
//  PracticeWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/20.
//

import WidgetKit
import SwiftUI
import SwiftData

typealias TaskProgress = TaskItemSchemaV1.TaskProgress
typealias TaskItem = TaskItemSchemaV1.TaskItem

struct Provider: AppIntentTimelineProvider {
    let modelContext = ModelContext(TaskItemSchemaV1.container)
    
    func snapshot(for configuration: ConfigurationYearProcessCardIntent, in context: Context) async -> SimpleEntry {
        await SimpleEntry(date: Date(), taskProcess: getTaskProcess(of: getYearFrom(date: .now)))
    }
    
    func timeline(for configuration: ConfigurationYearProcessCardIntent, in context: Context) async -> Timeline<SimpleEntry> {
        let year = configuration.year
        return await Timeline(entries: [SimpleEntry(date: firstDateOfYear(of: year), taskProcess: getTaskProcess(of: year))], policy: .atEnd)
    }
    
    typealias Entry = SimpleEntry
    
    typealias Intent = ConfigurationYearProcessCardIntent
    
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), taskProcess: [])
    }

    @MainActor
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
        let entry = SimpleEntry(date: Date(), taskProcess: getTaskProcess(of: getYearFrom(date: .now)))
        completion(entry)
    }
    
    @MainActor
    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> ()) {
        let entries: [SimpleEntry] = [SimpleEntry(date: Date(), taskProcess: getTaskProcess(of: getYearFrom(date: .now)))]
        let timeline = Timeline(entries: entries, policy: .never)
        completion(timeline)
    }
    
    @MainActor
    private func getTaskProcess(of year: Int) -> [TaskProgress] {
        let descriptor = FetchDescriptor<TaskProgress>()
        let taskProgress = try? modelContext.container.mainContext.fetch(descriptor)
        let filteredTaskProcess = taskProgress?.filter { progressItem in
            progressItem.status == .finished
        } ?? []
        
        return groupTaskProgressByYear(items: filteredTaskProcess)[firstDateOfYear(of: year)] ?? []
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let taskProcess: [TaskProgress]
}

struct PracticeWidgetEntryView : View {
    var entry: SimpleEntry

    var body: some View {
        HStack {
            GeometryReader { proxy in
                let cellWidth = CGFloat(ceil(Double((proxy.size.width * 0.8) / 31) * 0.65))
                YearProcessWidget(date: entry.date, taskProgress: entry.taskProcess, cellWidth: cellWidth)
            }
        }
    }
}

struct PracticeWidget: Widget {
    let kind: String = "PracticeWidget"

    var body: some WidgetConfiguration {
        AppIntentConfiguration(
            kind: kind,
            intent: ConfigurationYearProcessCardIntent.self,
            provider: Provider()
        ) { entry in
            PracticeWidgetEntryView(entry: entry)
                .containerBackground(.background, for: .widget)
        }
        .supportedFamilies([.systemMedium])
        .configurationDisplayName("Practice Time Card")
    }
}

#Preview(as: .systemMedium) {
    PracticeWidget()
} timeline: {
    SimpleEntry(date: .now, taskProcess: [])
}

//
//  YearProcessWidget.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/20.
//

import SwiftUI
import SwiftData

struct YearProcessWidget: View {
    private let modelContext = ModelContext(TaskItemSchemaV1.container)
    private var personalConfig: PersonalConfig?
    
    var date: Date = Date()
    var taskProgressDictionary: [Date: [TaskProgress]] = [:]
    private var cellWidth: CGFloat
    private var taskProgress: [TaskProgress] = []
    private let rows: Int = 12 // Fixed number of rows
    private let calendar = Calendar.current
    private var numberOfDaysInYear: Int {
        calendar.range(of: .day, in: .year, for: date)?.count ?? 0
    }
    private var yearName: Int {
        let components = calendar.dateComponents([.year, .month], from: date)
        return components.year!
    }
    
    private var columns: [GridItem] {
        let totalColumns = Int(ceil(Double(numberOfDaysInYear) / Double(rows)))
        return Array(repeating: .init(.flexible()), count: totalColumns)
    }
    
    private var datesInYear: [Date] {
        (1...numberOfDaysInYear).compactMap { day -> Date? in
            var components = calendar.dateComponents([.year, .month], from: date)
            components.day = day
            return calendar.date(from: components)
        }
    }
    
    // gourp by months
    private var datesInYearArr: [[Date]] {
        var monthsArray = [[Date]]()
        // Start by finding the first day of the year
        guard let _ = calendar.date(from: DateComponents(year: yearName, month: 1, day: 1)) else { return [] }
        // Iterate through each month
        for month in 1...12 {
            var monthDays = [Date]()
            guard let rangeOfDays = calendar.range(of: .day, in: .month, for: calendar.date(from: DateComponents(year: yearName, month: month))!) else {
                continue
            }
            // Iterate through each day of the month
            for day in rangeOfDays {
                if let date = calendar.date(from: DateComponents(year: yearName, month: month, day: day)) {
                    monthDays.append(date)
                }
            }
            monthsArray.append(monthDays)
        }
        
        return monthsArray
    }

    @MainActor
    init(date: Date, taskProgress: [TaskProgress], cellWidth: CGFloat) {
        self.date = date
        self.taskProgress = taskProgress
        self.cellWidth = cellWidth
        self.taskProgressDictionary = taskProgress.reduce(into: [:]) { result, progress in
            let key = calendar.startOfDay(for: progress.date)
            if result[key] == nil {
                result[key] = [progress]
            } else {
                result[key]?.append(progress)
            }
        }
        setPersonalConfig()
    }
    
    @MainActor private mutating func setPersonalConfig () {
        let descriptor = FetchDescriptor<PersonalConfig>()
        let personalConfigs = try? modelContext.container.mainContext.fetch(descriptor)
        let personalConfig = personalConfigs?.first
        self.personalConfig = personalConfig
    }
    
    private func color(for date: Date) -> Color {
        let practiceTime = if let date = taskProgressDictionary[calendar.startOfDay(for: date)] {
            getPracticeTime(of: date)
        } else {
            0
        }
        return calcCardCellColorFrom(seconds: practiceTime, targetTime: personalConfig?.dailyPracticeGoal ?? nil)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                Text(String(yearName))
                    .font(.footnote)
                    .fontWeight(.bold)
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                let seconds = getPracticeTime(of: taskProgress)
                Text(getTimeString(of: seconds))
                    .font(.caption)
                    .bold()
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                    .padding(.leading, 20)
                let practiceDays = getPracticeDays(of : taskProgress)
                Text("\(practiceDays) days")
                    .font(.caption)
                    .bold()
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                    .padding(.leading, 20)
                Spacer()
                Text("Practice")
                    .font(.footnote)
                    .fontWeight(.bold)
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
            }
            Spacer()
            HStack(spacing: 0) {
                VStack(spacing: cellWidth * 0.5) {
                    ForEach(datesInYearArr, id: \.self) { dateArr in
                        VStack(spacing: cellWidth * 0.5) {
                            HStack(spacing: cellWidth * 0.5) {
                                ForEach(dateArr, id: \.self) { date in
                                    Rectangle()
                                        .frame(width: cellWidth, height: cellWidth)
                                        .foregroundColor(color(for: date))
                                        .cornerRadius(2)
                                }
                                Spacer()
                            }.padding(0)
                        }.padding(0)
                    }
                }.padding(.vertical, 0)
                
                VStack {
                    ForEach(0..<6, id: \.self) { month in
                        Text("\(getMonthAbbrList()[month * 2])")
                            .font(.system(size: cellWidth * 1.3))
                            .foregroundColor(.gray)
                            .fontWeight(/*@START_MENU_TOKEN@*/.bold/*@END_MENU_TOKEN@*/)
                            .padding(0)
                        Spacer()
                    }
                }
            }
        }
    }
}

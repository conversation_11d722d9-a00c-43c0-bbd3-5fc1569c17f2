//
//  AppIntent.swift
//  PracticeWidget
//
//  Created by Ulyssis on 2024/2/20.
//

import WidgetKit
import AppIntents

struct ConfigurationYearProcessCardIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource = "Yearly progress card"
    static var description = IntentDescription("Yearly progress card.")

    // An example configurable parameter.
    @Parameter(title: "Year", optionsProvider: YearOptionsProvider())
    var year: Int
    
    init() {
        self.year = getYearFrom(date: .now)
    }
    
    struct YearOptionsProvider: DynamicOptionsProvider {
        func results() -> [Int] {
            return Array(2000...getYearFrom(date: .now))
        }
    }
}

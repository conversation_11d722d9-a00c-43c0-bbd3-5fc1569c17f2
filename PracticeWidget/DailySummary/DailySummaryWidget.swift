//
//  DailySummaryWidget.swift
//  practice
//
//  Created by 陈昇 on 6/3/24.
//

import SwiftUI
import WidgetKit
import SwiftData

struct DailySummaryProvider: TimelineProvider {
    func placeholder(in context: Context) -> DailySummaryEntry {
        let entries: [DailySummaryEntry] = [DailySummaryEntry(date: Date(), taskProcess: [])]
        let timeline = Timeline(entries: entries, policy: .never)
//        completion(timeline)
        return DailySummaryEntry(date: .now, taskProcess: [])
    }
    
    func getSnapshot(in context: Context, completion: @escaping (DailySummaryEntry) -> Void) {
//        return DailySummaryEntry(date: .now, taskProcess: [])
        let entries: [DailySummaryEntry] = [DailySummaryEntry(date: Date(), taskProcess: [])]
        let timeline = Timeline(entries: entries, policy: .never)
//        completion(timeline)
    }
    
    let modelContext = ModelContext(TaskItemSchemaV1.container)
     
    @MainActor
    func getTimeline(in context: Context, completion: @escaping (Timeline<DailySummaryEntry>) -> ()) {
        let entries: [DailySummaryEntry] = [DailySummaryEntry(date: Date(), taskProcess: [])]
        let timeline = Timeline(entries: entries, policy: .never)
        completion(timeline)
    }
}

struct DailySummaryEntry: TimelineEntry {
    let date: Date
    let taskProcess: [TaskProgress]
}

struct DailySummaryView : View {
    var entry: DailySummaryEntry
    
    var body: some View {
        DailySummaryWidgetView()
    }
}

struct DailySummaryWidget: Widget {
    let kind: String = "PracticeWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: DailySummaryProvider()
        ) { entry in
            DailySummaryView(entry: entry)
                .containerBackground(.background, for: .widget)
        }
        .supportedFamilies([.systemSmall])
        .configurationDisplayName("Daily Practice Summary")
    }
}

//#Preview(as: .systemMedium) {
//    PracticeWidget()
//} timeline: {
//    SimpleEntry(date: .now, taskProcess: [])
//}


//#Preview {
//    DailySummaryWidget()
//}

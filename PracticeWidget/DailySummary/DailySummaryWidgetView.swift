//
//  DailySummaryWidget.swift
//  practice
//
//  Created by 陈昇 on 6/3/24.
//

import SwiftUI
import Charts
import SwiftData

struct DailySummaryWidgetView: View {
    private var taskProgressData = TaskProgressData.shared
    
//    private let modelContext = ModelContext(TaskItemSchemaV1.container)
//    
//    var taskProgressDictionary: [Date: [TaskProgress]] = [:]
//    private var taskProgress: [TaskProgress] = []
    
    var body: some View {
        VStack(spacing: 0) {
            let list = taskProgressData.getTodayPracticeList
            let practiceTimeList = list.map { $0.1 }
            let practiceTime = practiceTimeList.reduce(0, +)
            
//            VStack (alignment: .leading, spacing: 0) {
//                HStack {
//                    Text("Today Practice")
//                        .font(.footnote)
//                        .foregroundColor(.gray)
//                        .fontWeight(.bold)
//                        .padding(.bottom)
//                    Spacer()
//                    Text("\(getTimeString(of: practiceTime))")
//                        .font(.footnote)
//                        .fontWeight(.bold)
//                        .padding(.bottom)
//                }
//                Chart(list, id: \.0) {element in
//                    BarMark(
//                        x: .value("practice time", CGFloat(element.1) / 3600),
//                        y: .value("piece name", element.0)
////                        width: 5
//                    )
//                    .cornerRadius(3.0)
//                    .annotation(position: .top, alignment: .leading, spacing: 5, content: {
//                        HStack {
//                            Text(element.0)
//                                .font(.footnote)
//                                .fontWeight(.bold)
//                                .foregroundColor(.gray)
//                            
//                            Text("\(getTimeString(of: element.1))")
//                                .font(.footnote)
//                                .fontWeight(.bold)
//                                .foregroundColor(.gray)
//                        }
//                    })
//                    .foregroundStyle(by: .value("piece name", element.0))
//                }
//                .chartXAxis(.hidden)
//                .chartYAxis(.hidden)
//                .chartLegend(.hidden)
////                .frame(height: CGFloat(list.count) * 10)
////                Spacer()
//            }
        }
    }
}

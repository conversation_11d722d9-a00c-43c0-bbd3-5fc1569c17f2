# 项目介绍

Only Practice 是一款专为练琴人设计的 App，提供 iPad 和 iPhone 版本。主要功能包括：

1. 根据练习曲目来创建任务
2. 计时功能，可以实时记录用户的练琴时长，也可手动添加记录
3. 录音功能，可以通过录音功能来记录用户的演奏，会自动归类到相应的曲目下，按照时间顺序整理，用户也可以手动导入录音文件。录音文件支持分享导出。
4. 成就展示，当用户完成某一项任务的练习时，可以将其状态更新为完成。所有完成的任务会展示在成就 Tab 下。

# 核心数据结构

1. Task Item：用户所创建的练习任务

```swift
@Model
final class TaskItem: Identifiable, Equatable {
    var id = UUID()
    var pieceName: String = ""
    var composerName: String = ""
    var beginDate: Date = Date()
    var finishedDate: Date = Date()
    var coverImage: Data? // 保留以向后兼容旧数据
    var coverImagePath: String? // 新的存储方式，使用相对路径
    var taskType: TaskType? = TaskType.Ongoing
    var lastModified: Date? = Date.now
    
    @Relationship(deleteRule: .cascade, inverse: \TaskProgress.task)
    var taskProgress: [TaskProgress]? = []
    
    @Relationship(deleteRule: .cascade, inverse: \RecordingItem.task)
    var recordings: [RecordingItem]? = []
    
//        @Relationship(deleteRule: .cascade, inverse: \VideoItem.task)
//        var videos: [VideoItem]? = []
    
    init(pieceName: String, composerName: String, key: MusicalKey, difficulty: Level, beginDate: Date, taskType: TaskType = .Ongoing, taskProgress: [TaskProgress] = [], coverImagePath: String? = nil) {
        self.pieceName = pieceName
        self.composerName = composerName
        self.key = key
        self.difficulty = difficulty
        self.beginDate = beginDate
        self.taskProgress = taskProgress
        self.taskType = taskType
        self.coverImagePath = coverImagePath
    }
}
```

1. Task Progress：记录用户的练习时长

```swift
@Model
final class TaskProgress: Identifiable {
    var date: Date = Date.now
    var status: TaskStatus = TaskStatus.notStart
    var practiceTime: Int? = 0 // seconds
    @Relationship var task: TaskItem?
    
    init(date: Date, status: TaskStatus = .notStart) {
        self.date = date
        self.status = status
    }
}
```

1. Recording Item：记录用户的录音文件

```swift
@Model
final class RecordingItem: Identifiable {
    var date: Date = Date.now
    var fileUrl: String = ""
    var isFavorite: Bool = false
    @Relationship var task: TaskItem?
    
    init(date: Date, fileUrl: String) {
        self.date = date
        self.fileUrl = fileUrl
        self.isFavorite = false
    }
}
```

1. Collection Item：任务成就集，可以将一部分任务归类到某个合集下面

```swift
@Model
final class CollectionItem: Identifiable {
    var cover: String = ""
    // 合集的封面
    var name: String = ""
    // 当前集合出现在列表中的顺序，用户可能通过手动拖拽来改变顺序
    var order: Int = 0    
    // delete rule 是，删除集合的时候，不删除任务
    @Relationship(deleteRule: TODO)
    // task items 要支持顺序，用户可能会通过手动拖拽任务来排序
  var taskItems: [TaskItem]? = []
  var type: Default | UserCreate
}
```

# 成就墙

## 目标

让用户在练完一首曲目之后，能够在成就墙上展示自己的努力成果，使得用户在一次次的练琴之后得到成就感。

## 功能

1. 创建合集

默认会有一个“全部合集”，里面是用户所有的“已完成”状态的任务；

除此之外，用户可以自定义创建自己的合集，自定义创建的合集支持编辑：合集名称、选择收录哪些已完成的任务；

用户可以为自己的合集选择封面图片（确定比例大小以适应不同设备的尺寸），

1. 合集排序

支持用户拖拽来按照自己的喜好排序各个合集

1. 展示合集里“已收录几个成就”

## 核心要点

1. 样式上要美观、让用户感受到“成就感”
2. 可以有一个封面图，支持让用户自定义更换

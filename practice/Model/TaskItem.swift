//
//  TaskItem.swift
//  practice
//
//  Created by U<PERSON><PERSON><PERSON> on 2024/1/29.
//

import Foundation
import SwiftData

enum Level: String, CaseIterable, Identifiable, Codable {
    case one = "1"
    case two = "2"
    case three = "3"
    case four = "4"
    case five = "5"
    case six = "6"
    case seven = "7"
    case eight = "8"
    case nine = "9"
    case ten = "10"
    case tenPlus = "10+"
    
    var id: String { self.rawValue }
}

enum MusicalKey: String, CaseIterable, Identifiable, Codable {
    case cMajor = "C major"
    case gMajor = "G major"
    case dMajor = "D major"
    case aMajor = "A major"
    case eMajor = "E major"
    case bMajor = "B major"
    case fSharpMajor = "F# major"
    case cSharpMajor = "C# major"
    case fMajor = "F major"
    case bFlatMajor = "Bb major"
    case eFlatMajor = "Eb major"
    case aFlatMajor = "Ab major"
    case dFlatMajor = "Db major"
    case gFlatMajor = "Gb major"
    case cFlatMajor = "Cb major"
    
    case aMinor = "A minor"
    case eMinor = "E minor"
    case bMinor = "B minor"
    case fSharpMinor = "F# minor"
    case cSharpMinor = "C# minor"
    case gSharpMinor = "G# minor"
    case dSharpMinor = "D# minor"
    case aSharpMinor = "A# minor"
    
    case dMinor = "D minor"
    case gMinor = "G minor"
    case cMinor = "C minor"
    case fMinor = "F minor"
    case bFlatMinor = "Bb minor"
    case eFlatMinor = "Eb minor"
    case aFlatMinor = "Ab minor"
    
    var id: String { self.rawValue }
}



enum TaskStatus: Codable, CaseIterable {
    case notStart, finished
}

enum TaskType: String, CaseIterable, Codable, Identifiable {
    case Target = "Target"
    case Ongoing = "Ongoing"
    case Achievement = "Achievement"
    case Default = "Default"
    var id: String { self.rawValue }
}
enum CollectionType: String, CaseIterable, Codable {
    case defaultCollection = "default"
    case userCreated = "userCreated"
}

enum TaskItemSchemaV1: VersionedSchema {
    static var models: [any PersistentModel.Type] {
//        [TaskItem.self, TaskProgress.self, RecordingItem.self, VideoItem.self]
        [TaskItem.self, TaskProgress.self, RecordingItem.self, VideoItem.self, CollectionItem.self]
    }

    static var versionIdentifier: Schema.Version = .init(1, 0, 3)
    
    @Model
    final class TaskItem: Identifiable, Equatable {
        var id = UUID()
        var pieceName: String = ""
        var composerName: String = ""
        var key: MusicalKey = MusicalKey.cMajor
        var difficulty: Level = Level.one
        var beginDate: Date = Date()
        var finishedDate: Date = Date()
        var coverImage: Data? // 保留以向后兼容旧数据
        var coverImagePath: String? // 新的存储方式，使用相对路径
        var taskType: TaskType? = TaskType.Ongoing
        var lastModified: Date? = Date.now
        
        @Relationship(deleteRule: .cascade, inverse: \TaskProgress.task)
        var taskProgress: [TaskProgress]? = []
        
        @Relationship(deleteRule: .cascade, inverse: \RecordingItem.task)
        var recordings: [RecordingItem]? = []
        
        @Relationship(deleteRule: .cascade, inverse: \VideoItem.task)
        var videos: [VideoItem]? = []
        
        @Relationship
        var collections: [CollectionItem]? = []
        
        init(pieceName: String, composerName: String, key: MusicalKey, difficulty: Level, beginDate: Date, taskType: TaskType = .Ongoing, taskProgress: [TaskProgress] = [], coverImagePath: String? = nil) {
            self.pieceName = pieceName
            self.composerName = composerName
            self.key = key
            self.difficulty = difficulty
            self.beginDate = beginDate
            self.taskProgress = taskProgress
            self.taskType = taskType
            self.coverImagePath = coverImagePath
        }
        
        static func == (lhs: TaskItem, rhs: TaskItem) -> Bool {
            return lhs.id == rhs.id
        }
        
        static func getDefaultTask() -> TaskItem {
            let task = TaskItem(pieceName: "练习曲", composerName: "肖邦", key: .cMajor, difficulty: .one, beginDate: Date.now)
            task.taskType = .Default
            return task
        }
        
        // 在原有的练习数据上增加
        func addPracticeTime(at date: Date, by seconds: Int) {
            let startOfDay = Calendar.current.startOfDay(for: date)
            if let progress = taskProgress?.first(where: { progress in
                return Calendar.current.isDate(progress.date, inSameDayAs: startOfDay)
            }) {
                progress.practiceTime = (progress.practiceTime ?? 0) + seconds
            } else {
//                该日期不存在练习数据，新建
                let progressItem = TaskProgress(
                    date: date,
                    status: .finished
                )
                progressItem.practiceTime = seconds
                self.taskProgress?.append(progressItem)
                self.lastModified = .now
            }
        }
        
        // 获取最近练习日期
        var mostRecentPracticeDate: Date {
            guard let taskProgress = taskProgress else { return Date.distantPast }

            let finishedProgress = taskProgress.filter { $0.status == .finished && ($0.practiceTime ?? 0) > 0 }

            if let mostRecentDate = finishedProgress.map({ $0.date }).max() {
                return mostRecentDate
            }

            return Date.distantPast
        }

        // 将练习任务按时间归类
        func getPracticeTimeList(
            from startDate: Date,
            to endDate: Date,
            by component: Calendar.Component
        ) -> [(Date, Int)] {
            let calendar = Calendar.current
            var date = calendar.startOfDay(for: startDate)
            let end = calendar.startOfDay(for: endDate)
            var dateBuckets: [Date: Int] = [:]

            // 初始化每一天或每个月的 bucket
            while date <= end {
                dateBuckets[date] = 0
                if let next = calendar.date(byAdding: component, value: 1, to: date) {
                    date = next
                } else {
                    break
                }
            }

            let taskProgress = taskProgress ?? []
            
            for progress in taskProgress {
                guard progress.status == .finished,
                      let practiceTime = progress.practiceTime
                else { continue }

                let progressDate = progress.date

                // 按天或月归档
                let bucketDate: Date
                switch component {
                case .day:
                    bucketDate = calendar.startOfDay(for: progressDate)
                case .month:
                    let components = calendar.dateComponents([.year, .month], from: progressDate)
                    bucketDate = calendar.date(from: components) ?? calendar.startOfDay(for: progressDate)
                default:
                    continue
                }

                if bucketDate >= startDate && bucketDate <= endDate {
                    dateBuckets[bucketDate, default: 0] += practiceTime
                }
            }

            return dateBuckets
                .sorted(by: { $0.key < $1.key })
                .map { ($0.key, $0.value) }
        }
    }
    
    @Model
    final class TaskProgress: Identifiable {
        var date: Date = Date.now
        var status: TaskStatus = TaskStatus.notStart
        var practiceTime: Int? = 0 // seconds
        @Relationship var task: TaskItem?
        
        init(date: Date, status: TaskStatus = .notStart) {
            self.date = date
            self.status = status
        }
    }
    
    @Model
    final class RecordingItem: Identifiable {
//        var id = UUID()
        var date: Date = Date.now
        var fileUrl: String = ""
        var isFavorite: Bool = false
        var note: String = ""
        @Relationship var task: TaskItem?
        
        init(date: Date, fileUrl: String) {
            self.date = date
            self.fileUrl = fileUrl
            self.isFavorite = false
            self.note = ""
        }
    }
    
    @Model
    final class CollectionItem: Identifiable {
        var id = UUID()
        var cover: String = ""           // Cover image path/URL
        var name: String = ""            // Collection name
        var desc: String = ""
        var order: Int = 0               // Display order (supports drag-and-drop reordering)
        var type: CollectionType = CollectionType.userCreated // Collection type
        var createdDate: Date = Date()
        var lastModified: Date = Date()

        // Relationship configuration:
        // - Delete rule: When collection is deleted, preserve the TaskItem objects
        // - Support ordering: TaskItems within collection should be reorderable via drag-and-drop
        @Relationship
        var taskItems: [TaskItem]? = []

        init(name: String, cover: String = "", desc: String = "", type: CollectionType = .userCreated, order: Int = 0) {
            self.name = name
            self.desc = desc
            self.cover = cover
            self.type = type
            self.order = order
            self.createdDate = Date()
            self.lastModified = Date()
            self.taskItems = []
        }

        // Computed property to get achievement count
        var achievementCount: Int {
            return taskItems?.count ?? 0
        }

        // Helper method to add task to collection
        func addTask(_ task: TaskItem) {
            if taskItems?.contains(task) == false {
                taskItems?.append(task)
                lastModified = Date()
            }
        }

        // Helper method to remove task from collection
        func removeTask(_ task: TaskItem) {
            taskItems?.removeAll { $0.id == task.id }
            lastModified = Date()
        }

        // Helper method to reorder tasks
        func reorderTasks(from source: IndexSet, to destination: Int) {
            taskItems?.move(fromOffsets: source, toOffset: destination)
            lastModified = Date()
        }
    }

    @Model
    final class VideoItem: Identifiable {
        var date: Date = Date.now
        var fileUrl: String = ""
        var isFavorite: Bool = false
        @Relationship var task: TaskItem?

        init(date: Date, fileUrl: String) {
            self.date = date
            self.fileUrl = fileUrl
            self.isFavorite = false
        }
    }
    
    static let container: ModelContainer = {
        do {
            // 创建带有 CloudKit 配置的 ModelConfiguration
            let configuration = ModelConfiguration(
                "Practice", 
                schema: schema,
                cloudKitDatabase: .private("iCloud.com.shawnchenxmu.onlypractice")
            )
            
            return try ModelContainer(for: schema, configurations: configuration)
        } catch {
            fatalError("⚠️ SwiftData 容器初始化失败: \(error.localizedDescription)")
        }
    }()
    
    static let schema = Schema([
        TaskItem.self,
        PersonalConfig.self,
        CollectionItem.self
    ])
}

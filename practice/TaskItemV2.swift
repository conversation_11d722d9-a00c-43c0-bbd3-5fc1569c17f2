import Foundation
import SwiftData

@Model
final class TaskItemV2 {
    var name: String
    var desc: String
    var isCompleted: Bool
    var createdAt: Date
    var imageFileName: String?
    
    init(name: String, desc: String, isCompleted: Bool = false, imageFileName: String? = nil) {
        self.name = name
        self.desc = desc
        self.isCompleted = isCompleted
        self.createdAt = Date()
        self.imageFileName = imageFileName
    }
    
    // 从TaskItem迁移
    convenience init(from taskItem: TaskItem) {
        self.init(
            name: taskItem.name, 
            desc: taskItem.desc, 
            isCompleted: taskItem.isCompleted
        )
        self.createdAt = taskItem.createdAt
    }
} 
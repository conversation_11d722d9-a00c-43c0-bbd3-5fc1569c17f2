//
//  RenderTest.swift
//  practice
//
//  Created by Assistant on 2025/6/23.
//

import SwiftUI
import SwiftData

struct RenderTestView: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false
    
    let imageManager = ImageManager(modelContext: TaskItemSchemaV1.container.mainContext)
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Render Performance Test")
                    .font(.title)
                    .bold()
                
                if isRunning {
                    ProgressView("Running tests...")
                        .padding()
                }
                
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        ForEach(testResults, id: \.self) { result in
                            Text(result)
                                .font(.system(.caption, design: .monospaced))
                                .padding(.horizontal)
                        }
                    }
                }
                .frame(maxHeight: 300)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                Button("Run Render Tests") {
                    runTests()
                }
                .disabled(isRunning)
                .buttonStyle(.borderedProminent)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Render Test")
        }
    }
    
    private func runTests() {
        isRunning = true
        testResults.removeAll()
        
        Task {
            await testSmallCollection()
            await testLargeCollection()
            await testVeryLargeCollection()
            
            await MainActor.run {
                isRunning = false
                testResults.append("✅ All tests completed!")
            }
        }
    }
    
    private func testSmallCollection() async {
        await MainActor.run {
            testResults.append("🧪 Testing small collection (5 tasks)...")
        }
        
        let collection = createTestCollection(taskCount: 5, name: "Small Test")
        await testRenderPerformance(collection: collection)
    }
    
    private func testLargeCollection() async {
        await MainActor.run {
            testResults.append("🧪 Testing large collection (25 tasks)...")
        }
        
        let collection = createTestCollection(taskCount: 25, name: "Large Test")
        await testRenderPerformance(collection: collection)
    }
    
    private func testVeryLargeCollection() async {
        await MainActor.run {
            testResults.append("🧪 Testing very large collection (50 tasks)...")
        }
        
        let collection = createTestCollection(taskCount: 50, name: "Very Large Test")
        await testRenderPerformance(collection: collection)
    }
    
    private func createTestCollection(taskCount: Int, name: String) -> CollectionItem {
        let collection = CollectionItem(name: name, desc: "Test collection with \(taskCount) tasks", type: .userCreated)
        
        var tasks: [TaskItem] = []
        for i in 1...taskCount {
            let task = TaskItem(
                pieceName: "Test Piece \(i)",
                composerName: "Test Composer \(i % 5 + 1)",
                key: .cMajor,
                difficulty: .five,
                beginDate: Date()
            )
            tasks.append(task)
        }
        
        collection.taskItems = tasks
        return collection
    }
    
    private func testRenderPerformance(collection: CollectionItem) async {
        let startTime = Date()

        await MainActor.run {
            let detailView = CollectionDetailView(collection: collection)
                .environmentObject(CollectionManager.shared)
                .environmentObject(NavigationManager.shared)
                .environmentObject(imageManager)
                .environmentObject(RecordingManager(modelContext: TaskItemSchemaV1.container.mainContext))

            // 模拟渲染调用
            detailView.render {
                let endTime = Date()
                let duration = endTime.timeIntervalSince(startTime)

                self.testResults.append("⏱️ \(collection.name): \(String(format: "%.2f", duration))s")

                if let coverData = detailView.coverData {
                    let image = coverData.renderedUIImage
                    let scale = image.scale
                    let actualSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)

                    self.testResults.append("📏 Image size: \(image.size)")
                    self.testResults.append("🔍 Scale: \(scale)x")
                    self.testResults.append("📐 Actual pixels: \(Int(actualSize.width))×\(Int(actualSize.height))")

                    // 质量评估
                    let pixelCount = actualSize.width * actualSize.height
                    if pixelCount > 2_000_000 {
                        self.testResults.append("✨ High quality render")
                    } else if pixelCount > 1_000_000 {
                        self.testResults.append("📱 Medium quality render")
                    } else {
                        self.testResults.append("⚠️ Low quality render")
                    }
                } else {
                    self.testResults.append("❌ Render failed for \(collection.name)")
                }
            }
        }

        // 等待渲染完成
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
    }
}

#Preview {
    RenderTestView()
}

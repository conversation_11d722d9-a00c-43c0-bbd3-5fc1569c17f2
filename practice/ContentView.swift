//
//  ContentView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @StateObject private var taskListManager: TaskListManager
    @StateObject private var configManager: ConfigManager
    @StateObject private var ratingManager = RatingManager.shared
    @StateObject private var recordingManager: RecordingManager
    @StateObject private var imageManager: ImageManager
    @StateObject private var membershipManager = MembershipManager.shared
    @StateObject private var collectionManager = CollectionManager.shared
//    @StateObject private var videoManager: VideoManager
    
    @StateObject private var taskListNavManager = NavigationManager()
    @StateObject private var achievementListNavManager = NavigationManager()
    @StateObject private var statisticsNavManager = NavigationManager()
    @StateObject private var settingsNavManager = NavigationManager()
    @StateObject private var colorThemeManager = ColorThemeManager.shared
    
    @State private var showOnboarding: Bool = false
    @State private var loading: Bool = true
    @State private var didAppear = false
    
    @SceneStorage("BottomTabView.selectedTab") private var selectedTab = "Practice"
    
    private let modelContext: ModelContext
    private let isIPad = UIDevice.current.userInterfaceIdiom == .pad


    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        _taskListManager = StateObject(wrappedValue: TaskListManager())
        _configManager = StateObject(wrappedValue: ConfigManager())
        _recordingManager = StateObject(wrappedValue: RecordingManager(modelContext: modelContext))
        _imageManager = StateObject(wrappedValue: ImageManager(modelContext: modelContext))
//        _videoManager = StateObject(wrappedValue: VideoManager(modelContext: modelContext))
    }

    var body: some View {
        VStack {
            if loading {
                LoadingView()
            } else {
                TabView (selection: $selectedTab) {
                    PracticeView()
                        .tabItem {
                            Image(systemName: "bubbles.and.sparkles.fill")
                            if isIPad {
                                Text("Practice")
                            }
                        }
                        .tag("Practice")
                    
                    NavigationStack(path: $taskListNavManager.path) {
                        CurrentListView()
                            .navigationDestination(
                                for: NavigationPage.self,
                                destination: NavigationManager.getDestinationPageFrom
                            )
                    }
                    .environmentObject(taskListNavManager)
                    .tabItem {
                        Image(systemName: "target")
                        if isIPad {
                            Text("Task")
                        }
                    }
                    .tag("CurrentList")
                    NavigationStack(path: $achievementListNavManager.path) {
                        CollectionListView()
                            .navigationDestination(
                                for: NavigationPage.self,
                                destination: NavigationManager.getDestinationPageFrom
                            )
                    }
                    .environmentObject(achievementListNavManager)
                    .tabItem {
                        Image(systemName: "trophy.fill")
                        if isIPad {
                            Text("Achievement")
                        }
                    }
                    .tag("AchievementList")
                    NavigationStack(path: $statisticsNavManager.path) {
                        StatisticsView()
                            .navigationDestination(
                                for: NavigationPage.self,
                                destination: NavigationManager.getDestinationPageFrom
                            )
                    }
                    .environmentObject(statisticsNavManager)
                    .tabItem {
                        Image(systemName: "chart.bar.fill")
                        if isIPad {
                            Text("Statistics")
                        }
                    }
                    .tag("Statistics")
                    NavigationStack(path: $settingsNavManager.path) {
                        SettingsView()
                            .navigationDestination(
                                for: NavigationPage.self,
                                destination: NavigationManager.getDestinationPageFrom
                            )
                    }
                    .environmentObject(settingsNavManager)
                    .tabItem {
                        Image(systemName: "gearshape.fill")
                        if isIPad {
                            Text("Settings")
                        }
                    }
                    .tag("Settings")
                }
            }
        }
        .onChange(of: selectedTab) {
            HapticManager.lightImpact()
        }
        .preferredColorScheme(colorThemeManager.preferredColorScheme)
        .onAppear {
            guard !didAppear else { return }
            didAppear = true

            Task {
                let start = Date()
                await MainActor.run {
                    loading = true
                }

//                let t1 = Date()
//                taskListManager.autoCreateDefaultTask()
//                print("⏱️ [\(Date().timeIntervalSince(t1))s] taskListManager.autoCreateDefaultTask() done")

                let t2 = Date()
                configManager.checkConfig()
                print("⏱️ [\(Date().timeIntervalSince(t2))s] configManager.checkConfig() done")

                let t5 = Date()
                await membershipManager.updateMembershipStatus()
                print("⏱️ [\(Date().timeIntervalSince(t5))s] membershipManager.updateMembershipStatus() done")

                await MainActor.run {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        loading = false
                    }
                    showOnboarding = !OnboardingView.hasSeenOnboarding
                    print("⏱️ [\(Date().timeIntervalSince(start))s] All done")
                }

                imageManager.migrateExistingCoverImages()
                imageManager.cleanupRedundantBinaryData()
            }
        }
        .fullScreenCover(isPresented: $showOnboarding) {
            OnboardingView()
        }
        .environmentObject(taskListManager)
        .environmentObject(configManager)
        .environmentObject(ratingManager)
        .environmentObject(recordingManager)
        .environmentObject(imageManager)
        .environmentObject(membershipManager)
        .environmentObject(collectionManager)
//        .environmentObject(videoManager)
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container
    ContentView(modelContext: modelContext.mainContext)
}


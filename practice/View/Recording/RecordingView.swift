//
//  RecordingView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/16.
//

import SwiftUI
import AVFoundation

struct RecordingView: View {
    @Environment(\.presentationMode) var presentationMode
    
    @EnvironmentObject var recordingManager: RecordingManager
    @Environment(\.dismiss) var dismiss
    
    @State private var sliderValue: Double = 0.0
    @State private var duration: TimeInterval = .zero

    let task: TaskItem
    @State private var showSaveConfirmation = false
    
    var headerView: some View {
        VStack {
            HStack {
                Spacer()
                Button(action: {
                    if recordingManager.status == .notStart {
                        dismiss()
                    } else {
                        showSaveConfirmation = true
                    }
                }, label: {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .bold()
                }).padding()
            }
            Spacer()
        }
    }
    
    var body: some View {
        VStack(alignment: .center, spacing: 30) {
            Spacer()
            // MARK: - Title
            Image(systemName: "mic.fill")
                .font(.largeTitle)
//                .padding(.bottom)
                .foregroundStyle(.secondary)
            Text(task.pieceName)
                .font(.largeTitle)
                .bold()
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // MARK: - Waveform (mocked with pulsating circle)
            WaveformView(powerLevels: recordingManager.powerLevels)
                .frame(height: 60)
                .padding(.horizontal)
            
            // MARK: - Time Display
            Text(timeString(from: recordingManager.recordingTime))
                .font(.system(size: 100))
                .fontWeight(.black)
                .scaledToFit()
                .minimumScaleFactor(0.6)
                .lineLimit(1)
                .monospacedDigit()
                .padding(.horizontal)
                .frame(height: 120)
            
            // MARK: - Control Buttons
            HStack {
                Group {
                    if recordingManager.status == .inProgress {
                        Button(action: {
                            recordingManager.pauseRecording()
                        }) {
                            Label("Pause", systemImage: "pause.fill")
                                .padding(.horizontal, 30)
                        }
                    } else if recordingManager.status == .pause {
                        Button(action: {
                            recordingManager.resumeRecording()
                        }) {
                            Label("Resume", systemImage: "play.fill")
                                .padding(.horizontal, 30)
                        }
                    } else if recordingManager.status == .notStart {
                        Button(action: {
                            recordingManager.startRecording(pieceId: task.id.uuidString)
                        }) {
                            Label("Start", systemImage: "play.fill")
                                .padding(.horizontal, 30)
                        }
                        .font(.title)
                        .fontWeight(.black)
                        .cornerRadius(30)
                    }
                    
                    if recordingManager.status == .inProgress || recordingManager.status == .pause {
                        Button(action: {
                            recordingManager.stopRecording(pieceId: task.id.uuidString)
                        }) {
                            Label("End", systemImage: "stop.fill")
                                .foregroundColor(.white)
                                .padding(.horizontal, 30)
                        }
                        .frame(height: 60)
                        .background(.red)
                    }
                }
                .foregroundStyle(.white)
                .font(.title)
                .fontWeight(.black)
                .frame(height: 60)
                .background(.teal)
                .cornerRadius(30)
            }
            
            // MARK: - Playback and Save/Delete Area
            if let url = recordingManager.lastRecordingURL {
                PlaybackSliderView(
                    sliderValue: $sliderValue,
                    duration: duration,
                    currentTime: recordingManager.playbackTime,
                    onSeek: { time in
                        recordingManager.seekPlayback(to: time)
                    }
                )
                .foregroundColor(.white)
                .onAppear {
                    Task {
                        if let recordingDuration = await recordingManager.getDurationFromUrl(url) {
                            duration = recordingDuration
                        }
                    }
                }
                
                HStack(spacing: 30) {
                    Group {
                        if recordingManager.isPlaying {
                            Button(action: {
                                recordingManager.stopPlayback()
                            }) {
                                VStack {
                                    Image(systemName: "stop.fill")
                                    Spacer()
                                    Text("Pause")
                                        .font(.headline)
                                }
                                .frame(height: 70)
                            }
                            .foregroundStyle(.gray)
                            .background(.clear)
                        } else {
                            Button(action: {
                                recordingManager.playRecording(from: url)
                            }) {
                                VStack {
                                    Image(systemName: "play.fill")
                                    Spacer()
                                    Text("Play")
                                        .font(.headline)
                                }
                                .frame(height: 70)
                            }
                            .background(.clear)
                            .foregroundStyle(.gray)
                        }
                        
                        Button(action: {
                            recordingManager.discardRecording()
                            sliderValue = 0
                        }) {
                            VStack {
                                Image(systemName: "trash.fill")
                                Spacer()
                                Text("Delete")
                                    .font(.headline)
                            }
                            .frame(height: 70)
                        }
                        .foregroundColor(.red)
                        .background(.clear)
                    }
                    .font(.title)
                    .fontWeight(.black)
                }
                
                VStack {
                    Button(action: {
                        recordingManager.saveRecording(task: task, url: url)
                        dismiss()
                        recordingManager.discardRecording()
                    }) {
                        Label("Save", systemImage: "flag.checkered")
                            .padding(.horizontal, 30)
                    }
                    .font(.title)
                    .fontWeight(.black)
                    .frame(height: 60)
                    .foregroundStyle(.white)
                    .background(.orange)
                    .cornerRadius(30)
                }
                HStack {
                    Text("Size: \(recordingManager.getFileSize(url: url))")
                        .foregroundColor(.white.opacity(0.5))
                        .fontWeight(.bold)
                        .font(.headline)
                }
            }
            
            Spacer()
        }
        .padding()
        .confirmationDialog("Recording is not saved yet. Do you want to exit?", isPresented: $showSaveConfirmation, titleVisibility: .visible) {
            Button("Exit") {
                dismiss()
                recordingManager.discardRecording()
            }
            Button("Continue Recording", role: .cancel) {}
        }
        .background(Color(.systemBackground))
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .overlay {
            headerView
                .safeAreaPadding(.all)
                .padding(.top)
        }
        .edgesIgnoringSafeArea(.top)
    }
}


import SwiftUI
import ActivityKit
import WidgetKit

struct CountingUpTimerAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var startDate: Date
        var taskName: String
    }
}

struct CountingUpTimerLiveActivityView: View {
    var context: ActivityViewContext<CountingUpTimerAttributes>
    
    var body: some View {
        VStack {
            HStack (alignment: .top) {
                Text("Practice")
                    .font(.headline)
                Spacer()
                Text("\(context.state.taskName)")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.trailing)
            }
            HStack {
                Text(timerInterval: context.state.startDate...(.distantFuture), countsDown: false)
                    .font(.largeTitle)
                    .padding()
                    .bold()
                    .multilineTextAlignment(.center)
                Spacer()
            }
        }
        .padding()
    }
}

struct CountingUpTimerLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: CountingUpTimerAttributes.self) { context in
            CountingUpTimerLiveActivityView(context: context)
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading) {
                    Text("Practice")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text(timerInterval: context.state.startDate...(.distantFuture), countsDown: false)
                }
            } compactLeading: {
                Text("Practice")
            } compactTrailing: {
                Text(timerInterval: context.state.startDate...(.distantFuture), countsDown: false)
            } minimal: {
                Text(timerInterval: context.state.startDate...(.distantFuture), countsDown: false)
            }
        }
    }
}

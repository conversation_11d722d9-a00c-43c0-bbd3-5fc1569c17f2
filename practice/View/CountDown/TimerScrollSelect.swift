//
//  TimerScrollSelect.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON>s on 2024/3/5.
//

import SwiftUI

struct TimeSelectScroll: View {
    private var numRange = Array(stride(from: 0, to: 100, by: 5))
    var body: some View {
        ZStack {
            ScrollView (.horizontal) {
                HStack {
                    ForEach(numRange, id: \.self) { num in
                        NumUnit(num: num)
                    }
                }
            }
            IndicatorPointer
        }
    }
    
    var IndicatorPointer: some View {
        VStack {
            Cell()
            Rectangle()
                .frame(width: 20, height: 20)
                .foregroundColor(.orange)
        }
    }
    
    struct NumUnit: View {
        var num: Int
        var isActive = false
        var body: some View {
            VStack {
                Text("\(num)")
                    .font(.system(size: 30))
                    .bold()
                    .foregroundColor(isActive ? .black : .gray)
                HStack(alignment: .top) {
                    Cell(height: 30)
                    Cell(height: 30)
                    Cell()
                    Cell(height: 30)
                    Cell(height: 30)
                }
            }
        }
    }

    struct Cell: View {
        var height = 50
        var body: some View {
            HStack {
                Rectangle()
                    .frame(width: 4, height: CGFloat(height))
                    .cornerRadius(2)
                    .foregroundColor(.gray)
            }
        }
    }
}

#Preview {
    TimeSelectScroll()
}

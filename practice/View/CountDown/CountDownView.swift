import SwiftUI
import ActivityKit

struct CountDownView: View {
    typealias CountDownStatus = CountDownContainer.CountDownStatus
    
    var minute: Int
    let task: TaskItem
    @Binding var showCloseConfirm: Bool
    @Binding var status: CountDownStatus
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.scenePhase) private var scenePhase
    @StateObject private var notificationManager = NotificationManager.shared

//    Timer
    @SceneStorage("CountDownView.timerActive") private var timerActive = false {
        didSet {
            print("timerActive", timerActive)
            if timerActive {
                startTimerActivity()
                setEndTime()
            } else {
                endTimerActivity()
            }
        }
    }

    @SceneStorage("CountDownView.remainingTime") private var remainingTime: Int = 0
    @SceneStorage("CountDownView.endTime") private var endTime: String?
    private var passedSecond: Int {
        minute * 60 - remainingTime
    }

    @State private var timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    @State private var countdownTimerActivity: Activity<CountdownTimerAttributes>?
    private let countingActivityIdentifier = "countingActivityIdentifier"
    
    private let notificationIdentifier = "countDownTimerNotification"
    
    let onEnd: (_ second: Int) -> ()

    var body: some View {
        ZStack {
            VStack {
                TimerCountDownView
            }
        }
        .onChange(of: scenePhase) {
            switch scenePhase {
            case .background:
                if timerActive {
                    stopTimerAndScheduleNotification()
                }
            case .active:
                if timerActive {
                    calculateRemainingTimeAndResumeTimer()
                }
            default:
                break
            }
        }
        .onDisappear {
            // This ensures the cancellation happens when the view disappears.
            print("onDesappear CountDownView")
            resetState()
            endTimerActivity()
            cancelExistingNotifications()
            // 停止音频
            notificationManager.stopCountdownSound()
        }
        .onAppear() {
            if timerActive {
                calculateRemainingTimeAndResumeTimer()
            }
        }
        .confirmationDialog(
            "Close",
            isPresented: $showCloseConfirm,
            titleVisibility: .visible,
            actions: {
                Button {
                    presentationMode.wrappedValue.dismiss()
                    onEnd(passedSecond)
                } label: {
                    Text("Quit and Record Practice Time")
                }
                Button(role: .destructive) {
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    Text("Quit")
                }
            }, message: {
                Text("Are you sure you want to quit?")
            }
        )
    }
    
    var TimerCountDownView: some View {
        VStack {
            Group {
                if status == .notStart {
                    Text("\(timerString(from: minute * 60))")
                } else {
                    Text("\(timerString(from: remainingTime))")
                }
            }
            .font(.system(size: 100))
            .fontWeight(.black)
            .monospacedDigit()
            .frame(height: 120)
            .onReceive(timer) { _ in
                if !timerActive {
                    return
                }
//                avtive
                if remainingTime > 0 {
                    remainingTime = remainingTime - 1
                } else if remainingTime == 0 {
                    if status != .finished {
                        status = .finished
//                        TODO 验证不退出到后台，直接结束的情况
                        handleCountdownFinished()
                        timer.upstream.connect().cancel()
                    }
                }
            }
            buttonAction
        }
    }

    var buttonAction: some View {
        HStack {
            Button(action: {
                switch status {
                case .notStart:
                    handleTimerStart()
                case .inProgress:
                    handleTimerPause()
                case .pause:
                    handleTimerResume()
                case .finished:
                    handleTimerFinish()
                }
            }) {
                Group {
                    switch status {
                    case .notStart:
                        Label("Start", systemImage: "play.fill")
                    case .inProgress:
                        Label("Pause", systemImage: "pause.fill")
                    case .pause:
                        Label("Resume", systemImage: "play.fill")
                    case .finished:
                        Label("Finish", systemImage: "flag.checkered")
                    }
                }
                .padding(.horizontal, 30)
                .padding()
            }
            .font(.title)
            .fontWeight(.black)
            .frame(height: 60)
            .background(status == .finished ? .orange : timerActive ? .red : .teal)
            .cornerRadius(30)
        }
        .padding(.top, 50)
        .foregroundColor(.white)
    }
    
    
//    MARK: Button actions
    private func handleTimerStart() {
        notificationManager.requestNotificationPermission { _ in }
        remainingTime = minute * 60
        timerActive.toggle()
        status = .inProgress
    }
    
    private func handleTimerPause() {
        timerActive.toggle()
        status = .pause
    }
    
    private func handleTimerResume() {
        timerActive.toggle()
        status = .inProgress
    }
    
    private func handleTimerFinish() {
        onEnd(passedSecond)
        presentationMode.wrappedValue.dismiss()
    }

    private func handleCountdownFinished() {
        print("⏰ handleCountdownFinished called")
        print("   - countdownSoundEnabled: \(notificationManager.countdownSoundEnabled)")

        // 倒计时结束时直接播放音效（不使用通知）
        if notificationManager.countdownSoundEnabled {
            print("✅ Playing countdown finished sound directly")
            notificationManager.playCountdownFinishedSound(at: .zero)
        } else {
            print("❌ Sound disabled, skipping")
        }
    }
    
    private func setEndTime() {
        let future = Calendar.current.date(byAdding: .second, value: remainingTime, to: .now)
        if let future = future {
            endTime = getStringFrom(date: future)
        }
    }
    
    private func resetState() {
        timerActive = false
        status = .notStart
        remainingTime = 0
        endTime = nil
    }
    
//    MARK: Timer Activity
    
    private func startTimerActivity() {
        let future = Calendar.current.date(byAdding: .second, value: remainingTime, to: .now)
        if let future = future {
            endTime = getStringFrom(date: future)
            startCountdownTimer(targetDate: future)
        }
    }
    
    private func startCountdownTimer(targetDate: Date) {
        let initialContent = CountdownTimerAttributes.ContentState(remainingTime: Date()...targetDate, taskName: task.pieceName)
        let activityAttributes = CountdownTimerAttributes(targetDate: targetDate)
        
        do {
            countdownTimerActivity = try Activity.request(attributes: activityAttributes, content: ActivityContent(state: initialContent, staleDate: targetDate))
        } catch {
            print("Error starting countdown timer live activity: \(error.localizedDescription)")
        }
    }
    
    private func endTimerActivity() {
        Task {
            if let content = countdownTimerActivity?.content {
                await countdownTimerActivity?.end(content, dismissalPolicy: .immediate)
            }
        }
    }

//    MARK: Timer Notification
    private func scheduleNotification() {
        notificationManager.scheduleCountdownNotification(
            identifier: notificationIdentifier,
            title: String(localized: "Practice Timer Finished!"),
            body: String(localized: "Your practice task \(task.pieceName) has completed."),
            timeInterval: TimeInterval(remainingTime)
        )
        // handle sound and vibration
        notificationManager.playCountdownFinishedSound(at: TimeInterval(remainingTime))
    }

    private func cancelExistingNotifications() {
        notificationManager.cancelCountdownNotification(identifier: notificationIdentifier)
        notificationManager.stopCountdownSound()
    }
    
//    -------------------------------------
    
    private func stopTimerAndScheduleNotification() {
        self.timer.upstream.connect().cancel()
        cancelExistingNotifications()
        scheduleNotification()
    }
        
    private func calculateRemainingTimeAndResumeTimer() {
        // Calculate remaining time based on endTime
        print("calculateRemainingTimeAndResumeTimer")
        if let endTime = getDateFrom(string: endTime) {
            print("calculateRemainingTimeAndResumeTimer endTime", endTime)
            let now = Date()
            if now >= endTime {
                // Timer expired
                remainingTime = 0
                timerActive = false
                status = .finished
            } else {
                cancelExistingNotifications()
                print("calculateRemainingTimeAndResumeTimer Not Expired", !timerActive)
                remainingTime = Int(endTime.timeIntervalSince(now))
                if timerActive && status == .inProgress {
//                    restart timer
                    timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
                }
            }
        }
    }
}


#Preview {
    @State var showCloseConfirm = false
    @State var status = CountDownContainer.CountDownStatus.notStart
    return CountDownView(minute: 15, task: TaskItem(
        pieceName: "Nocturne Op. 09 Nr, 01",
        composerName: "Chopin",
        key: .aMinor,
        difficulty: .nine,
        beginDate: Date(),
        taskProgress: []
    ), showCloseConfirm: $showCloseConfirm, status: $status, onEnd: {time in })
}

//
//  CountDownContainer.swift
//  practice
//
//  Created by 陈昇 on 4/2/24.
//

import SwiftUI
import StoreKit

struct CountDownContainer: View {
    enum CountDownStatus: String {
        case notStart
        case inProgress
        case pause
        case finished
    }

    @Environment(\.requestReview) var requestReview
    @EnvironmentObject private var ratingManager: RatingManager
    @Environment(\.presentationMode) var presentationMode
    @SceneStorage("CountDownContainer.selectedCountDownTime") private var selectedCountDownTime: CountDownTimePicker.CountDownTimeOption = .fifteen

    @SceneStorage("CountDownContainer.status") private var status: CountDownStatus = .notStart
    @State private var showCloseConfirm = false
    
    let onEnd: (_ second: Int) -> ()
    let task: TaskItem
    
    var body: some View {
        VStack(alignment: .center) {
            Text("\(task.pieceName)")
                .font(.largeTitle)
                .foregroundColor(.secondary)
                .bold()
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            if (status == .notStart) {
                CountDownTimePicker(selectedTime: $selectedCountDownTime)
            }
            switch selectedCountDownTime {
            case .timer:
                CountingView(status: $status, showCloseConfirm: $showCloseConfirm, onEnd: self.onEnd, task: task)
            default:
                CountDownView(
                    minute: Int(selectedCountDownTime.rawValue) ?? 0,
                    task: task,
                    showCloseConfirm: $showCloseConfirm,
                    status: $status,
                    onEnd: self.onEnd
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .overlay {
            headerView
                .safeAreaPadding(.all)
                .padding(.top)
        }
        .edgesIgnoringSafeArea(.top)
        .onDisappear() {
            ratingManager.recordAction()
        }
    }
    
    var headerView: some View {
        VStack {
            HStack {
                Spacer()
                Button(action: {
                    if status == .notStart {
                        presentationMode.wrappedValue.dismiss()
                    } else {
                        showCloseConfirm = true
                    }
                }, label: {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .bold()
                }).padding()
            }
            Spacer()
        }
    }
}

#Preview {
    CountDownContainer(onEnd: {time in }, task: TaskItem(
        pieceName: "Nocturne Op. 09 Nr, 01",
        composerName: "Chopin",
        key: .aMinor,
        difficulty: .nine,
        beginDate: Date(),
        taskProgress: []
    ))
}

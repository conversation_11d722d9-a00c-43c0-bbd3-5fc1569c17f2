//
//  CountingView.swift
//  practice
//
//  Created by 陈昇 on 4/2/24.
//

import SwiftUI
import ActivityKit

struct CountingView: View {
    typealias CountDownStatus = CountDownContainer.CountDownStatus
    @Binding var status: CountDownStatus
    @Binding var showCloseConfirm: Bool
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.scenePhase) private var scenePhase
    
    @SceneStorage("CountingView.timerActive") private var timerActive = false {
        didSet {
            if timerActive {
                startTimerActivity()
            } else {
                endTimerActivity()
            }
        }
    }
    
    @SceneStorage("CountingView.passedSecond") private var passedSecond = 0
    @SceneStorage("CountingView.activePassedSecond") private var activePassedSecond = 0
    @SceneStorage("CountingView.lastStartTime") private var lastStartTime: Int?
    
    private var totalPassedSecond: Int {
        passedSecond + activePassedSecond
    }
    
    @State private var timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    let onEnd: (_ second: Int) -> ()
    let task: TaskItem
    
    @State private var countingTimerActivity: Activity<CountingUpTimerAttributes>?
    
    var body: some View {
        ZStack {
            VStack {
                TimerCountingView
            }
        }
        .onChange(of: scenePhase) {
            switch scenePhase {
            case .background:
                if timerActive {
                    stopTimer()
                }
            case .active:
                if timerActive {
                    resumeTimer()
                }
            default:
                break
            }
        }
        .onDisappear {
            // This ensures the cancellation happens when the view disappears.
            print("onDesappear CountingView")
            endTimerActivity()
            resetState()
        }
        .onAppear() {
            if timerActive {
                resumeTimer()
            }
        }
        .confirmationDialog(
            "Close",
            isPresented: $showCloseConfirm,
            titleVisibility: .visible,
            actions: {
                Button {
                    presentationMode.wrappedValue.dismiss()
                    onEnd(totalPassedSecond)
                } label: {
                    Text("Quit and Record Practice Time")
                }
                Button(role: .destructive) {
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    Text("Quit")
                }
            }, message: {
                Text("Are you sure you want to quit?")
            }
        )
    }
    
    var TimerCountingView: some View {
        VStack {
            Text("\(timerString(from: totalPassedSecond, showHour: true))")
                .font(.system(size: 100))
                .fontWeight(.black)
                .scaledToFit()
                .minimumScaleFactor(0.6)
                .lineLimit(1)
                .monospacedDigit()
                .padding(.horizontal)
                .frame(height: 120)
                .onReceive(timer) { _ in
                    if timerActive {
                        updatePassedSecond()
                    }
                }
            buttonAction
        }
    }
    
    var buttonAction: some View {
        HStack {
            Button(action: {
                switch status {
                case .notStart:
                    handleTimerStart()
                case .inProgress:
                    handleTimerPause()
                case .pause:
                    handleTimerResume()
                case .finished:
                    return
                }
            }) {
                Group {
                    switch status {
                    case .notStart:
                        Label("Start", systemImage: "play.fill")
                    case .inProgress:
                        Label("Pause", systemImage: "pause.fill")
                    case .pause:
                        Image(systemName: "play.fill")
                    case .finished:
                        Label("Finish", systemImage: "flag.checkered")
                    }
                }
                .padding(.horizontal, 30)
                .padding()
            }
            .font(.title)
            .fontWeight(.black)
            .frame(height: 60)
            .background(status == .finished ? .orange : timerActive ? .red : .teal)
            .cornerRadius(30)
            
            if status == .pause {
                Button(action: handleTimerFinish) {
                    Label("Finish", systemImage: "flag.checkered")
                        .padding(.horizontal, 30)
                }
                .font(.title)
                .fontWeight(.black)
                .frame(height: 60)
                .background(.orange)
                .cornerRadius(30)
            }
            
        }
        .padding(.top, 50)
        .foregroundColor(.white)
    }
    
    //    MARK: Button actions
    private func handleTimerStart() {
        lastStartTime = Int(Date().timeIntervalSince1970)
        timerActive.toggle()
        status = .inProgress
    }
    
    private func handleTimerPause() {
        passedSecond = passedSecond + activePassedSecond
        activePassedSecond = 0
        timerActive.toggle()
        status = .pause
    }
    
    private func handleTimerResume() {
        lastStartTime = Int(Date().timeIntervalSince1970)
        timerActive.toggle()
        status = .inProgress
    }
    
    private func handleTimerFinish() {
        onEnd(totalPassedSecond)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func updatePassedSecond() {
        if let lastStartTime = lastStartTime {
            activePassedSecond = Int(Date().timeIntervalSince1970) - lastStartTime
        }
    }
    
    
    //    MARK: Timer Activity
    private func startTimerActivity() {
        if let startDate = Calendar.current.date(byAdding: .second, value: -passedSecond, to: .now) {
            let initialContent = CountingUpTimerAttributes.ContentState(startDate: startDate, taskName: task.pieceName)
            let activityAttributes = CountingUpTimerAttributes()
            
            do {
                countingTimerActivity = try Activity.request( attributes: activityAttributes, content: ActivityContent(state: initialContent, staleDate: .distantFuture))
            } catch {
                print("Error starting countdown timer live activity: \(error.localizedDescription)")
            }
        }
    }
    
    private func endTimerActivity() {
        Task {
            if let content = countingTimerActivity?.content {
                await countingTimerActivity?.end(content, dismissalPolicy: .immediate)
            }
        }
    }
    
    
//    MARK: timer helper function
    private func resumeTimer() {
        updatePassedSecond()
        timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    }
    
    private func stopTimer() {
        timer.upstream.connect().cancel()
    }
    
    private func resetState() {
        timerActive = false
        status = .notStart
        passedSecond = 0
        activePassedSecond = 0
    }
}

//#Preview {
//    @State var showCloseConfirm = false
//    @State var status = CountDownContainer.CountDownStatus.notStart
//    return CountingView(status: $status, showCloseConfirm: $showCloseConfirm, onEnd: {time in})
//}

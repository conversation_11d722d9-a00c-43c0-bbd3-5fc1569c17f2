//
//  CountDownTimerPicker.swift
//  practice
//
//  Created by 陈昇 on 4/1/24.
//

import SwiftUI

struct CountDownTimePicker: View {
    enum CountDownTimeOption: String, Hashable {
//        Count Down
        case timer = "∞"
        case one = "1"
        case five = "5"
        case ten = "10"
        case fifteen = "15"
        case twenty = "20"
        case twentyfive = "25"
        case thirty = "30"
        case fortyfive = "45"
        case sixty = "60"
        
        var id: String { self.rawValue }
    }
    
    @Binding var selectedTime: CountDownTimeOption
    private let countDownTimes: [CountDownTimeOption] = [.timer, .five, .ten, .fifteen, .twenty, .twentyfive, .thirty, .fortyfive, .sixty]
    var body: some View {
        HStack {
            Text("Practice")
            Picker("", selection: $selectedTime) {
                ForEach(countDownTimes, id: \.self) {
                    Text("\($0.rawValue)")
                        .roundedTitle()
                }
            }
            .pickerStyle(.wheel)
            .frame(width: /*@START_MENU_TOKEN@*/100/*@END_MENU_TOKEN@*/)
            Text("min")
        }.font(.title)
    }
}

extension String {
    var isInt: Bool {
        return Int(self) != nil
    }
}

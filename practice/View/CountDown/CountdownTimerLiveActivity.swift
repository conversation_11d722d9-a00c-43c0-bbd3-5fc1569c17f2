//
//  CountdownTimerLiveActivity.swift
//  practice
//
//  Created by 陈昇 on 3/28/24.
//

import SwiftUI
import ActivityKit
import WidgetKit

struct CountdownTimerAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var remainingTime: ClosedRange<Date>
        var taskName: String
    }
    
    var targetDate: Date
}

struct CountdownTimerLiveActivityView: View {
    var context: ActivityViewContext<CountdownTimerAttributes>
    
    var body: some View {
        VStack {
            HStack (alignment: .top) {
                Text("Practice")
                    .font(.headline)
                Spacer()
                Text("\(context.state.taskName)")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.trailing)
            }
            HStack(alignment: .center) {
                Spacer()
                Text(timerInterval: context.state.remainingTime, countsDown: true)
                    .font(.largeTitle)
                    .padding()
                    .bold()
                    .multilineTextAlignment(.center)
                Spacer()
            }
        }
        .padding()
    }
}

struct CountdownTimerLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: CountdownTimerAttributes.self) { context in
            CountdownTimerLiveActivityView(context: context)
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading) {
                    Text("Practice")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text(timerInterval: context.state.remainingTime, countsDown: true)
                }
            } compactLeading: {
                Text("Practice")
            } compactTrailing: {
                Text(timerInterval: context.state.remainingTime, countsDown: true)
            } minimal: {
                Text(timerInterval: context.state.remainingTime, countsDown: true)
            }
        }
    }
    
    
}

//
//  CustomSubscribeView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/20.
//

import SwiftUI
import StoreKit

struct CustomSubscribeView: View {
    @Environment(\.dismiss) var dismiss
    private var membershipManager: MembershipManager = .shared
    @State private var selectedOption: String?
    @State private var isLoading = true
    @State private var products: [Product] = []
    @State private var freeOffers: [String: String?] = [:]
    
    enum SubscriptionOption: String, CaseIterable {
        case lifetime = "com.onlypractice.lifetime"
        case yearly = "com.onlypractice.yearly"
        case monthly = "com.onlypractice.monthly"
        
        var title: String {
            switch self {
            case .lifetime: return "终身"
            case .yearly: return "年付"
            case .monthly: return "月付"
            }
        }
        
        var subtitle: String {
            switch self {
            case .lifetime: return "仅支付一次"
            case .yearly, .monthly: return ""
            }
        }
    }
    
    var body: some View {
            VStack(spacing: 0) {
                HStack {
                    Text("Only Practice Pro")
                        .font(.title)
                        .fontWeight(.bold)
                    Spacer()
                    But<PERSON> {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark")
                            .font(.title3)
                            .foregroundColor(.gray)
                            .padding(8)
                    }
                }
                .padding(.horizontal)
                .padding(.top)
                ScrollView(.vertical) {
                    Image("Violinist")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 150)
                        .layoutPriority(1)
                    VStack(alignment: .center, spacing: 5) {
                        Text("Unlimited Task Items")
                            .font(.custom("Nunito-Bold", size: 20))
                        Text("With the free plan, you can create up to 10 practice tasks and enjoy all features. Subscribe to unlock unlimited task creation.")
                            .font(.custom("Nunito-Bold", size: 14))
                            .foregroundStyle(.primary.opacity(0.6))
                            .multilineTextAlignment(.center)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding(.horizontal)
                    
                    Spacer()
                        .frame(minHeight: 0, maxHeight: .infinity)
                    //                            .layoutPriority(1)
                    
                    // Subscription options
                    if isLoading && membershipManager.products.isEmpty {
                        ProgressView()
                            .padding()
                    } else {
                        VStack(spacing: 16) {
                            ForEach(products) {product in
                                let freeOffer = freeOffers[product.id] ?? nil
//                                let freeOffer = membershipManager.freeTrialDescription(for: product)
                                subscriptionCard(
                                    id: product.id,
                                    title: product.displayName,
                                    subtitle: product.description,
                                    price: product.displayPrice,
                                    discountBadge: freeOffer
                                )
                            }
                        }
                        
                        .padding()
                    }
                }
                Button {
                    purchase()
                } label: {
                    Text("Continue")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.accentColor)
                        .cornerRadius(12)
                        .padding(.horizontal)
                }
                .padding(.bottom, 10)
                
                // Restore and redeem code links
                HStack(spacing: 8) {
                    Button {
                        Task {
                            await membershipManager.restorePurchases()
                        }
                    } label: {
                        Text("Restore Purchases")
                            .font(.footnote)
                            .foregroundColor(.gray)
                    }
                    Link(destination: URL(string: "https://www.apple.com/legal/internet-services/itunes/dev/stdeula/")!) {
                        Text("Terms")
                            .font(.footnote)
                            .foregroundColor(.gray)
                    }
                    .buttonStyle(.plain)
                }
                .padding(.bottom, 8)
            }
            .background(Color(.secondarySystemBackground).ignoresSafeArea())
            .onAppear {
                Task {
                    isLoading = true
                    if membershipManager.products.isEmpty {
                        await membershipManager.loadProducts()
                    }
                    products = membershipManager.products
                    selectedOption = products.first?.id
                    isLoading = false
                    var temp: [String: String?] = [:]
                    for product in products {
                        let desc = await membershipManager.freeTrialDescription(for: product)
                        temp[product.id] = desc ?? nil
                    }
                    freeOffers = temp
                }
            }
        }
        
        private func subscriptionCard(id: String, title: String, subtitle: String, price: String, discountBadge: String? = nil) -> some View {
            Button {
                selectedOption = id
            } label: {
                HStack {
                    ZStack {
                        Image("Image")
                            .resizable()
                            .frame(width: 48, height: 48)
                            .font(.title3)
                            .cornerRadius(12)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(.headline)
                            .foregroundStyle(.primary)
                        
                        if !subtitle.isEmpty {
                            Text(subtitle)
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                    }
                    
                    Spacer()
                    
                    // Price and discount badge
                    ZStack(alignment: .topTrailing) {
                        Text(price)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.accentColor)
                            .padding(.trailing, discountBadge != nil ? 8 : 0)
                        
                        if let badge = discountBadge {
                            Text(badge)
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.accentColor)
                                .cornerRadius(8)
                                .offset(x: 8, y: -18)
                        }
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedOption == id ? Color.accentColor : Color.clear, lineWidth: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        
    private func purchase() {
        if let selectedOption = selectedOption, let product = membershipManager.getProduct(productID: selectedOption) {
            Task {
                do {
                    let success = try await membershipManager.purchase(product: product)
                    if success {
                        dismiss()
                    }
                } catch {
                    print("Purchase failed: \(error)")
                }
            }
        }
    }
}

#Preview {
    CustomSubscribeView()
}
    

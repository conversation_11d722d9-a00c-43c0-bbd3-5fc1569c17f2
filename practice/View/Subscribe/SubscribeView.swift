//
//  SubscribeView.swift
//  practice
//
//  Created by 陈昇 on 12/31/24.
//

import SwiftUI
import StoreKit

struct SubscribeView: View {
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject private var membershipManager: MembershipManager
    
    var body: some View {
        ProductView(id: "com.onlypractice.lifetime") {
            Image(systemName: "crown")
        }
        .productViewStyle(.compact)
        .padding()
        .onInAppPurchaseStart { product in
            print("User has started buying \(product.id)")
        }
        .onInAppPurchaseCompletion { product, result in
            if case .success(.success(let transaction)) = result {
                onPurchaseSuccess()
                print("Purchased successfully: \(transaction.signedDate)")
            } else {
                print("Something else happened")
            }
        }
        if #available(iOS 18.0, *) {
            subscriptionView
                .subscriptionStoreControlStyle(.compactPicker)
        } else {
            subscriptionView
                .subscriptionStoreControlStyle(.prominentPicker)
        }
    }
    
    private var subscriptionView: some View {
        SubscriptionStoreView(productIDs: ["com.onlypractice.lifetime", "com.onlypractice.yearly", "com.onlypractice.monthly"]) {
            VStack {
                Text("Only Practice Pro")
                    .font(.largeTitle)
                    .fontWeight(.black)
                
                Text("Unlimited Task Items")
                    .multilineTextAlignment(.center)
                    .padding(.bottom)
                
                VStack(alignment: .leading) {
                    HStack(alignment: .top) {
                        Text("1.")
                        Text("With the free plan, you can create up to 10 practice tasks and enjoy all features.")
                            .foregroundStyle(.primary.opacity(0.6))
                            .multilineTextAlignment(.leading)
                    }
                    HStack(alignment: .top) {
                        Text("2.")
                        Text("Want more than 10 tasks? Just subscribe to Only Practice Pro! (All tasks created during your subscription will stay even after it ends.)")
                            .foregroundStyle(.primary.opacity(0.6))
                            .multilineTextAlignment(.leading)
                    }
                    HStack(alignment: .top) {
                        Text("3.")
                        Text("Subscribing to Pro not only unlocks more features — it also supports the developer to keep improving the app!")
                            .foregroundStyle(.primary.opacity(0.6))
                            .multilineTextAlignment(.leading)
                    }
                    HStack(alignment: .top) {
                        Spacer()
                        Link(destination: URL(string: "https://www.apple.com/legal/internet-services/itunes/dev/stdeula/")!) {
                            Text("Terms")
                                .foregroundStyle(.white.opacity(0.6))
                                .underline()
                        }
                        .buttonStyle(.plain)
                    }
                    .padding(.vertical)
                }
                .padding(.horizontal)
                .font(.footnote)
            }
            .foregroundStyle(.white)
            .containerBackground(.accent.gradient, for: .subscriptionStore)
        }
        .onInAppPurchaseCompletion { product, result in
            if case .success(.success(let transaction)) = result {
//                TODO: toast!
                onPurchaseSuccess()
                print("Purchased successfully: \(transaction.signedDate)")
            } else {
                print("Something else happened")
            }
        }
        .storeButton(.visible, for: .restorePurchases)
    }
    
    private func onPurchaseSuccess() {
        dismiss()
        Task {
            await membershipManager.updateMembershipStatus()
        }
    }
}

#Preview {
    SubscribeView()
}

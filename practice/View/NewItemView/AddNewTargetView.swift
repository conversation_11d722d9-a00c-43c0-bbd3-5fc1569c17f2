//
//  AddNewItemView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI
import PhotosUI

struct AddNewTargetView: View {
    @Bindable var taskItem: TaskItem = TaskItem(
        pieceName: "",
        composerName: "",
        key: .cMajor,
        difficulty: .one,
        beginDate: Date()
    )
//    TODO what's the binding mean?
    @State var selectedImage: PhotosPickerItem?
//    TODO what's the \.modelContext mean
    @Environment(\.modelContext) private var modelContext
    var onCreate: () -> () = {}
    var onSave: () -> () = {}
    private var mode = EditMode.Add
    
    init(taskItem: TaskItem, onSave: @escaping () -> ()) {
        self.taskItem = taskItem
        self.onSave = onSave
        mode = EditMode.Edit
    }
    
    init(onCreate: @escaping () -> ()) {
        self.onCreate = onCreate
        mode = EditMode.Add
        taskItem.taskType = .Target
    }
    
    var body: some View {
        ZStack {
            VStack() {
                Form {
                    Section(header: Text("Piece name")) {
                        TextField("Piece name", text: $taskItem.pieceName)
                    }
                    
                    
                    Section(header: Text("Composer name")) {
                        TextField("Composer name", text: $taskItem.composerName)
                    }
                    
                    Section(){
                        Picker(selection: $taskItem.key, content: {
                            ForEach(MusicalKey.allCases) { key in
                                Text(key.rawValue).tag(key)
                            }
                        }) {
                            Label("Key", systemImage: "music.note.list")
                        }
                        Picker(selection: $taskItem.difficulty, content: {
                            ForEach(Level.allCases) { level in
                                Text(level.rawValue).tag(level)
                            }
                        }, label: {
                            Label("Difficulty", systemImage: "goforward.10")
                        })
                    }

                    Section() {
                        DatePicker(selection: $taskItem.beginDate, displayedComponents: .date, label: { Label("Start Date", systemImage: "calendar") })
                    }
                    
                    Section() {
                        PhotosPicker(selection: $selectedImage, matching: .images) {
                            Label("Select Cover", systemImage: "photo")
                        }
                        if let imageData = taskItem.coverImagePath, let uiImage = UIImage(data: imageData) {
                            Image(uiImage: uiImage).resizable().scaledToFit()
                        }
                    }
                }.onChange(of: selectedImage) {
                    Task {
                        taskItem.coverImage = try await selectedImage?.loadTransferable(type: Data.self)
                    }
                }
                if mode == .Add {
                    Button(action: {
                        modelContext.insert(taskItem)
                        onCreate()
                    }) {
                        Text("Create")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(.accent)
                            .cornerRadius(10)
                    }
                    .frame(maxWidth: .infinity, maxHeight: 50)
                    .padding()
                    .shadow(radius: 2)
                }
            }.navigationTitle(mode == .Add ? "Add New Target" : "Edit Target")
                .background(UITraitCollection.current.userInterfaceStyle == .dark ? Color(red: 0.108, green: 0.108, blue: 0.119) : Color(red: 0.949, green: 0.949, blue: 0.971))
        }
    }
}

#Preview {
    AddNewItemView(taskItem: TaskItem(
        pieceName: "",
        composerName: "",
        key: .cMajor,
        difficulty: .one,
        beginDate: Date()
    ), onSave: {}).modelContainer(for: TaskItem.self, inMemory: false)
}

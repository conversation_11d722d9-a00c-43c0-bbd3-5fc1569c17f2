//
//  AddNewAchievementView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
//

import SwiftUI
import PhotosUI

struct AddNewAchievementView: View {
    @Bindable var taskItem: TaskItem = TaskItem(
        pieceName: "",
        composerName: "",
        key: .cMajor,
        difficulty: .one,
        beginDate: Date()
    )
    @State var selectedImage: PhotosPickerItem?
    @Environment(\.modelContext) private var modelContext
    var onCreate: () -> () = {}
    var onSave: () -> () = {}
    private var mode = EditMode.Add
    
    init(taskItem: TaskItem, onSave: @escaping () -> ()) {
        self.taskItem = taskItem
        self.onSave = onSave
        mode = EditMode.Edit
    }
    
    init(onCreate: @escaping () -> ()) {
        self.onCreate = onCreate
        mode = EditMode.Add
        taskItem.taskType = .Target
    }
    
    var body: some View {
        ZStack {
            VStack() {
                Form {
                    Section(header: Text("Piece name")) {
                        TextField("Piece name", text: $taskItem.pieceName)
                    }
                    
                    
                    Section(header: Text("Composer name")) {
                        TextField("Composer name", text: $taskItem.composerName)
                    }

                    Section() {
                        DatePicker(selection: $taskItem.beginDate, displayedComponents: .date, label: { Label("Start Date", systemImage: "calendar") })
                        DatePicker(selection: $taskItem.finishedDate, displayedComponents: .date, label: { Label("Finished Date", systemImage: "calendar") })
                    }
                    
                    Section() {
                        PhotosPicker(selection: $selectedImage, matching: .images) {
                            Label("Select Cover", systemImage: "photo")
                        }
                        if let imageData = taskItem.coverImage, let uiImage = UIImage(data: imageData) {
                            Image(uiImage: uiImage).resizable().scaledToFit()
                        }
                    }
                }.onChange(of: selectedImage) {
                    //          TODO what is task doing
                    Task {
                        taskItem.coverImage = try await selectedImage?.loadTransferable(type: Data.self)
                    }
                }
                if mode == .Add {
                    Button(action: {
                        modelContext.insert(taskItem)
                        onCreate()
                    }) {
                        Text("Create")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(.accent)
                            .cornerRadius(10)
                    }
                    .frame(maxWidth: .infinity, maxHeight: 50)
                    .padding()
                    .shadow(radius: 2)
                }
            }.navigationTitle(mode == .Add ? "Add New Achievement" : "Edit Achievement")
                .background(UITraitCollection.current.userInterfaceStyle == .dark ? Color(red: 0.108, green: 0.108, blue: 0.119) : Color(red: 0.949, green: 0.949, blue: 0.971))
        }
    }
}

#Preview {
    AddNewAchievementView(taskItem: TaskItem(
        pieceName: "",
        composerName: "",
        key: .cMajor,
        difficulty: .one,
        beginDate: Date()
    ), onSave: {}).modelContainer(for: TaskItem.self, inMemory: false)
}

////
////  VideoItemView.swift
////  practice
////
////  Created by <PERSON> on 2025/6/3.
////
//
//import SwiftUI
//import AVKit
//
//struct VideoItemView: View {
//    let video: VideoItem
//    @Environment(\.modelContext) private var modelContext
//    @Binding var selectedVideo: VideoItem?
//    @Binding var showDeleteConfirmation: Bool
//    @Binding var videoToDelete: VideoItem?
//    @State private var showShareSheet = false
//    @State private var showPlayer = false
//    @State private var player: AVPlayer? = nil
//    @EnvironmentObject var videoManager: VideoManager
//    
//    var body: some View {
//        let url = videoManager.storage.resolveURL(for: video.fileUrl)
//        VStack(alignment: .leading) {
//            HStack {
//                VStack(alignment: .leading) {
//                    Text(video.date.formatted(date: .abbreviated, time: .shortened))
//                        .font(.subheadline)
//                        .fontWeight(.bold)
//                        .foregroundColor(.primary)
//                    Text(getFileSize(url: url))
//                        .foregroundColor(.secondary)
//                        .font(.caption)
//                        .fontWeight(.bold)
//                }
//                Spacer()
//                Button(action: {
//                    video.isFavorite.toggle()
//                    try? modelContext.save()
//                }) {
//                    Image(systemName: "star.fill")
//                        .font(.title)
//                        .foregroundColor(.accent.opacity(video.isFavorite ? 1 : 0.1))
//                }
//                .buttonStyle(.plain)
//                Button(action: {
//                    selectedVideo = video
//                }) {
//                    Image(systemName: "play.circle.fill")
//                        .font(.title)
//                        .foregroundColor(.accent)
//                }
//                .buttonStyle(.plain)
//            }
//        }
//        .padding()
//        .background(Color(.systemBackground))
//        .cornerRadius(10)
//        .swipeActions(edge: .trailing) {
//            Button {
//                videoToDelete = video
//                showDeleteConfirmation = true
//            } label: {
//                Label("Delete", systemImage: "trash.fill")
//            }
//            .tint(.red)
//            Button {
//                showShareSheet = true
//            } label: {
//                Label("Share", systemImage: "square.and.arrow.up")
//            }
//            .tint(.blue)
//        }
//        .confirmationDialog(
//            "Delete Video",
//            isPresented: $showDeleteConfirmation,
//            titleVisibility: .visible,
//            presenting: videoToDelete
//        ) { video in
//            Button("Delete", role: .destructive) {
//                videoManager.deleteVideo(video)
//            }
//            Button("Cancel", role: .cancel) {
//                videoToDelete = nil
//            }
//        } message: { video in
//            Text("Are you sure you want to delete this video? This action cannot be undone.")
//        }
//        .sheet(isPresented: $showShareSheet) {
//            ShareSheet(activityItems: [url])
//        }
//        .sheet(item: $selectedVideo, onDismiss: {
//            selectedVideo = nil
//        }) { video in
//            let url = videoManager.storage.resolveURL(for: video.fileUrl)
//            ZStack {
//                Color.black.ignoresSafeArea()
//                VideoPlayer(player: AVPlayer(url: url))
//                    .ignoresSafeArea()
//            }
//        }
//    }
//
//    func getFileSize(url: URL) -> String {
//        do {
//            let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
//            if let size = resourceValues.fileSize {
//                let sizeInMB = Double(size) / (1024.0 * 1024.0)
//                return String(format: "%.2f MB", sizeInMB)
//            }
//        } catch {
//            print("❌ Failed to get file size: \(error)")
//        }
//        return "Unknown size"
//    }
//}

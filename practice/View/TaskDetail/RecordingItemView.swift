//
//  SwiftUIView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/17.
//

import SwiftUI
import UIKit

struct RecordingItemView: View {
    let recording: RecordingItem
    @Environment(\.modelContext) private var modelContext
    @Binding var selectedRecording: RecordingItem?
    @Binding var sliderValue: Double
    @Binding var showDeleteConfirmation: Bool
    @Binding var recordingToDelete: RecordingItem?
    @State private var durationText: String = ""
    
    @EnvironmentObject var recordingManager: RecordingManager
    
    @State private var showShareSheet = false
    @State private var showEditSheet = false

    var body: some View {
        let url = recordingManager.storage.resolveURL(for: recording.fileUrl)
        VStack(alignment: .leading) {
            HStack {
                VStack(alignment: .leading) {
                    Text(recording.date.formatted(date: .abbreviated, time: .shortened))
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    HStack {
                        Text(durationText)
                            .foregroundColor(.secondary)
                            .font(.caption)
                            .fontWeight(.bold)
                        Text(recordingManager.getFileSize(url: url))
                            .foregroundColor(.secondary)
                            .font(.caption)
                            .fontWeight(.bold)
                    }
                }

                Spacer()

                Button(action: {
                    recordingManager.toggleFavorite(of: recording)
                }) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.accentColor.opacity(recording.isFavorite ? 1 : 0.1))
                }
                .buttonStyle(.plain)

                Button(action: {
                    if selectedRecording?.id == recording.id && recordingManager.isPlaying {
                        recordingManager.stopPlayback()
                    } else {
                        selectedRecording = recording
                        recordingManager.playRecording(from: url)
                    }
                }) {
                    Image(systemName: selectedRecording?.id == recording.id && recordingManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                        .font(.system(size: 28, weight: .bold))
//                        .foregroundColor(.accent)
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(.plain)

                // Context Menu Button
                Menu {
                    Button {
                        showEditSheet = true
                    } label: {
                        Label("Edit", systemImage: "pencil")
                    }

                    Button {
                        showShareSheet = true
                    } label: {
                        Label("Share", systemImage: "square.and.arrow.up")
                    }

                    Button(role: .destructive) {
                        recordingToDelete = recording
                        showDeleteConfirmation = true
                    } label: {
                        Label("Delete", systemImage: "trash.fill")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle.fill")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(.plain)
            }

            if selectedRecording?.id == recording.id {
                PlaybackSliderView(
                    sliderValue: $sliderValue,
                    duration: recordingManager.playbackDuration,
                    currentTime: recordingManager.playbackTime,
                    onSeek: { time in
                        recordingManager.seekPlayback(to: time)
                    }
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
//        .swipeActions(edge: .trailing) {
//            Button {
//                recordingToDelete = recording
//                showDeleteConfirmation = true
//            } label: {
//                Label("Delete", systemImage: "trash.fill")
//            }
//            .tint(.red)
//            Button {
//                showShareSheet = true
//            } label: {
//                Label("Share", systemImage: "square.and.arrow.up")
//            }
//            .tint(.blue)
//        }
        .confirmationDialog(
            "Delete Recording",
            isPresented: $showDeleteConfirmation,
            titleVisibility: .visible,
            presenting: recordingToDelete
        ) { recording in
            Button("Delete", role: .destructive) {
                recordingManager.deleteRecording(recording)
            }
            Button("Cancel", role: .cancel) {
                recordingToDelete = nil
            }
        } message: { recording in
            Text("Are you sure you want to delete this recording? This action cannot be undone.")
        }
        .sheet(isPresented: $showShareSheet) {
            ShareSheet(activityItems: [url])
        }
        .sheet(isPresented: $showEditSheet) {
            RecordingEditView(recording: recording)
        }
        .onAppear {
            if durationText != "" { return }
            Task {
                if let duration = await recordingManager.getDurationFromUrl(url) {
                    durationText = timeString(from: duration)
                } else {
                    durationText = ""
                }
            }
        }
    }
}

// ShareSheet 封装
struct ShareSheet: UIViewControllerRepresentable {
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

//
//  RecordingEditView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/25.
//

import SwiftUI

struct RecordingEditView: View {
    let recording: RecordingItem
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    // Form state
    @State private var editedDate: Date
    @State private var editedNote: String
    
    // UI state
    @State private var focusedField: FocusField? = nil
    
    enum FocusField {
        case date, note
    }
    
    init(recording: RecordingItem) {
        self.recording = recording
        self._editedDate = State(initialValue: recording.date)
        self._editedNote = State(initialValue: recording.note)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
            
            // Form Content
            ScrollView {
                VStack(spacing: 24) {
                    // Date Section
                    dateSection
                        .padding(.horizontal)
                    
                    // Note Section
                    noteSection
                        .padding(.horizontal)
                }
                .padding(.vertical)
            }
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        ZStack {
            Text("Edit Recording")
                .font(.title3)
                .fontWeight(.semibold)
            
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                Spacer()
                <PERSON><PERSON>("Save") {
                    saveChanges()
                }
                .disabled(false) // 可以保存空的note
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Date Section
    private var dateSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Date")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    focusedField = focusedField == .date ? nil : .date
                }
            }) {
                HStack {
                    Text(editedDate.formatted(date: .abbreviated, time: .shortened))
                        .foregroundColor(.primary)
                    Spacer()
                    Image(systemName: "calendar")
                        .foregroundColor(.accentColor)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(.plain)
            
            if focusedField == .date {
                DatePicker(
                    "Select Date",
                    selection: $editedDate,
                    in: ...Date(),
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(.graphical)
                .labelsHidden()
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
    }
    
    // MARK: - Note Section
    private var noteSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Note")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    focusedField = focusedField == .note ? nil : .note
                }
            }) {
                HStack {
                    Text(editedNote.isEmpty ? "Add a note..." : editedNote)
                        .foregroundColor(editedNote.isEmpty ? .secondary : .primary)
                        .multilineTextAlignment(.leading)
                    Spacer()
                    Image(systemName: "note.text")
                        .foregroundColor(.accentColor)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(.plain)
            
            if focusedField == .note {
                VStack(spacing: 12) {
                    TextEditor(text: $editedNote)
                        .frame(minHeight: 100)
                        .padding(8)
                        .background(Color(.systemBackground))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                    
                    HStack {
                        Spacer()
                        Button("Done") {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                focusedField = nil
                            }
                        }
                        .font(.headline)
                        .foregroundColor(.accentColor)
                    }
                }
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
    }
    
    // MARK: - Actions
    private func saveChanges() {
        recording.date = editedDate
        recording.note = editedNote
        
        do {
            try modelContext.save()
            dismiss()
        } catch {
            print("Failed to save recording changes: \(error)")
        }
    }
}

#Preview {
    let recording = RecordingItem(date: Date(), fileUrl: "test.m4a")
    recording.note = "Test note"
    
    return RecordingEditView(recording: recording)
        .modelContainer(for: RecordingItem.self, inMemory: true)
}

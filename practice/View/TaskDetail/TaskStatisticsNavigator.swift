//
//  TaskStatisticsNavigator.swift
//  practice
//
//  Created by <PERSON> on 2025/4/19.
//

import SwiftUI
import Charts
import SwiftData

struct TaskStatisticsNavigator: View {
    let task: TaskItem
    @State private var startDate = Calendar.current.date(byAdding: .day, value: -7, to: Date())!
    @State private var unit = StatisticsUnit.week
    
    enum StatisticsUnit: String, Identifiable, CaseIterable {
        case week = "week"
        case month = "month"
        var id: String { self.rawValue }
        
        var localizedString: String {
            switch self {
            case .week:
                return String(localized: "Week")
            case .month:
                return String(localized: "Month")
            }
        }
    }
    
    var body: some View {
        VStack {
            Picker(selection: $unit, content: {
                ForEach(StatisticsUnit.allCases) { key in
                    Text(key.localizedString).tag(key)
                }
            }) {}
                .pickerStyle(.palette)
                .listRowSeparator(.hidden)
                .padding()
            PracticeChartView(task: task, unit: unit)
        }
    }
}

struct PracticeChartView: View {
    @Query private var tasks: [TaskItem]
    var task: TaskItem? {
        tasks.first { $0.id == taskID }
    }
    var taskID: PersistentIdentifier

    let unit: TaskStatisticsNavigator.StatisticsUnit

    @State private var selectedElement: (Date, Int)?
    @State private var selectedDate: Date?
    @State private var currentStart: Date = Calendar.current.startOfWeek(for: Date())
    
    init(task: TaskItem, unit: TaskStatisticsNavigator.StatisticsUnit) {
        self.taskID = task.id
        self.unit = unit
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            DateNavigationView(
                unit: unit, 
                currentStart: $currentStart, 
                canGoToPrev: canGoToPrev, 
                canGoToNext: canGoToNext,
                dateRangeString: dateRangeString,
                onPrev: goPrev,
                onNext: goNext
            )
            
            StatisticsSummaryView(practiceTimeList: practiceTimeList)
                .padding(.horizontal)
            
            ChartContainerView(
                data: practiceTimeList,
                unit: unit,
                showTarget: false,
                selectedDate: $selectedDate,
                selectedElement: $selectedElement
            )
        }
        .onChange(of: unit) {
            selectedDate = nil
            selectedElement = nil
        }
        .onChange(of: currentStart) {
            selectedDate = nil
            selectedElement = nil
        }
    }
    
    //  当前时间范围的结束日期
    private var endOfCurrentPeriod: Date {
        let calendar = Calendar.current
        switch unit {
        case .week:
            return calendar.endOfWeek(for: currentStart)
        case .month:
            return calendar.endOfMonth(for: currentStart)
        }
    }

    // MARK: date navigaton
    private var canGoToNext: Bool {
        endOfCurrentPeriod < Calendar.current.startOfDay(for: Date())
    }
    
    private var canGoToPrev: Bool {
        guard let earliestDate = (task?.taskProgress ?? []).map({ $0.date }).min() else {
            return false
        }
        return currentStart > earliestDate
    }
    
    private func goNext() {
        let calendar = Calendar.current
        let component: Calendar.Component = unit == .week ? .weekOfYear : .month
        if let next = calendar.date(byAdding: component, value: 1, to: currentStart) {
            currentStart = next
        }
    }
    
    private func goPrev() {
        let calendar = Calendar.current
        let component: Calendar.Component = unit == .week ? .weekOfYear : .month
        if let prev = calendar.date(byAdding: component, value: -1, to: currentStart) {
            currentStart = prev
        }
    }

    private var practiceTimeList: [(Date, Int)] {
        let calendar = Calendar.current
        switch unit {
        case .week:
            let start = calendar.startOfWeek(for: currentStart)
            let end = calendar.endOfWeek(for: currentStart)
            return task?.getPracticeTimeList(from: start, to: end, by: .day) ?? []
        case .month:
            let start = calendar.startOfMonth(for: currentStart)
            let end = calendar.endOfMonth(for: currentStart)
            return task?.getPracticeTimeList(from: start, to: end, by: .day) ?? []
        }
    }
    
    private var dateRangeString: String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        switch unit {
        case .week:
            formatter.dateFormat = "MMM d"
            let end = Calendar.current.date(byAdding: .day, value: 6, to: currentStart)!
            return "\(formatter.string(from: currentStart)) - \(formatter.string(from: end))"
        case .month:
            formatter.dateFormat = "MMMM yyyy"
            return formatter.string(from: currentStart)
        }
    }
}

// MARK: - Date Navigation Component
struct DateNavigationView: View {
    let unit: TaskStatisticsNavigator.StatisticsUnit
    @Binding var currentStart: Date
    let canGoToPrev: Bool
    let canGoToNext: Bool
    let dateRangeString: String
    let onPrev: () -> Void
    let onNext: () -> Void
    
    var body: some View {
        HStack {
            Button(action: onPrev) {
                Image(systemName: "arrowtriangle.backward.fill")
            }
            .contentShape(Rectangle())
            .buttonStyle(.plain)
            .disabled(!canGoToPrev)
            
            Spacer()
            
            Text(dateRangeString)
                .font(.headline)
            
            Spacer()
            
            Button(action: onNext) {
                Image(systemName: "arrowtriangle.forward.fill")
            }
            .contentShape(Rectangle())
            .buttonStyle(.plain)
            .disabled(!canGoToNext)
        }
        .padding(.horizontal)
    }
}

// MARK: - Statistics Summary Component
struct StatisticsSummaryView: View {
    let practiceTimeList: [(Date, Int)]
    
    var body: some View {
        let list = practiceTimeList.map({ $0.1 })
        let practiceTime = list.reduce(0, +)
        let (time, unit) = getTimeStringPair(of: practiceTime, decimal: 1)
        
        HStack(spacing: 0) {
            Spacer()
            VStack(alignment: .trailing) {
                HStack(alignment: .firstTextBaseline) {
                    Text("\(time)")
                        .roundedLargeTitle()
                        .foregroundColor(.primary)
                    Text("\(unit)")
                        .roundedBody()
                }
                Text("Average \(getTimeString(of: practiceTime / max(1, practiceTimeList.count)))")
                    .roundedBody()
            }
        }
        .font(.footnote)
        .fontWeight(.bold)
        .foregroundColor(.gray)
    }
}

// MARK: - Chart Container Component
struct ChartContainerView: View {
    let data: [(Date, Int)]
    let unit: TaskStatisticsNavigator.StatisticsUnit
    var showTarget = false
    @Binding var selectedDate: Date?
    @Binding var selectedElement: (Date, Int)?
    
    var body: some View {
        ChartView(
            data: data,
            unit: unit,
            selectedDate: selectedDate,
            selectedElement: selectedElement,
            showTarget: false
        )
        .padding()
        .frame(height: 220)
        .chartXSelection(value: $selectedDate)
        .onChange(of: selectedDate) {
            if let date = selectedDate {
                selectedElement = data.first(where: { Calendar.current.isDate($0.0, inSameDayAs: date) })
            }
        }
    }
}

struct ChartPointMark: View {
    var titleNumber: String
    var titleUnit: String
    var subTitle: String
    var body: some View {
        VStack {
            HStack(alignment: .firstTextBaseline, spacing: 5) {
                Text(titleNumber)
                    .roundedTitle()
                Text(titleUnit)
                    .roundedBody()
            }
            Text(subTitle)
                .roundedBody()
                .foregroundStyle(.secondary)
        }
        .font(.caption2)
        .padding(6)
        .background(
            Color(.systemBackground)
                .cornerRadius(5)
                .shadow(color: .primary.opacity(0.1), radius: 5)
        )
    }
}

// MARK: - Chart View Component
struct ChartView: View {
    let data: [(Date, Int)]
    let unit: TaskStatisticsNavigator.StatisticsUnit
    let selectedDate: Date?
    let selectedElement: (Date, Int)?
    var showTarget = false

    @State private var personalConfig: PersonalConfig?
    
    var body: some View {
        let maxTimeInHours = max(
            1,
            (data.map { Double($0.1) / 3600 } + (showTarget ? [Double(personalConfig?.dailyPracticeGoal ?? 0) / 3600 + 0.5] : []))
            .max() ?? 0
        )
        let beginRange = data.count > 0 ? data[0].0 : nil
        Chart {
            ForEach(data, id: \.0) { item in
                let isSelected = if let selectedDate = selectedDate {
                    Calendar.current.isDate(selectedDate, inSameDayAs: item.0)
                } else {
                    false
                }
                BarMark(
                    x: .value("Date", item.0, unit: .day),
                    y: .value("Time", Double(item.1) / 3600)
                )
                .foregroundStyle(.gray)
                .shadow(
                    color: isSelected ? .gray.opacity(0.7) : Color.clear,
                    radius: isSelected ? 5 : 0
                )
            }
            
            if let selected = selectedElement {
                let start = Calendar.current.startOfDay(for: selected.0)
                let midDay = Calendar.current.date(byAdding: .hour, value: 12, to: start)!
                
                // 虚线 RuleMark
                RuleMark(x: .value("Selected", midDay))
                    .foregroundStyle(.gray)
                    .lineStyle(StrokeStyle(lineWidth: 1, dash: [5]))
                
                if let beginRange = beginRange {
                    PointMark(
                        x: .value("Date", beginRange),
                        y: .value("Time", maxTimeInHours)
                    )
                    .symbolSize(0)
                    .annotation(position: .topTrailing) {
                        let (time, unit: timeUnit) = getTimeStringPair(of: selected.1)
                        ChartPointMark(titleNumber: time, titleUnit: timeUnit, subTitle: unit == .month ? getDateLocaleDesc(from: selected.0) : getDateString(from: selected.0, unit: unit))
                    }
                }
            }
            
            if showTarget, let dailyPracticeGoal = personalConfig?.dailyPracticeGoal {
                RuleMark(
                    y: .value("Target", CGFloat(dailyPracticeGoal) / 3600)
                )
                .foregroundStyle(.accent.opacity(0.5))
                .lineStyle(StrokeStyle(lineWidth: 4, lineCap: .round))
                .annotation(position: .top, alignment: .leading) {
                    HStack {
                        let targetTime = getTimeString(of: dailyPracticeGoal, decimal: 1)
                        Text("Target")
                            .roundedBody()
                            .lineLimit(1)
                            .layoutPriority(1)
                        Text("\(targetTime)")
                            .roundedBody()
                            .lineLimit(1)
                            .layoutPriority(1)
                    }
                    .shadow(color: Color(.systemBackground), radius: 2)
                    .foregroundStyle(.primary.opacity(0.5))
                }
            }
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: .day, count: 1)) { value in
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        let dayToDisplay = [1, 5, 10, 15, 20, 25]
                        if dayToDisplay.contains(Calendar.current.component(.day, from: date)) || unit == .week {
                            Text(getDateString(from: date, unit: unit))
                                .roundedBody()
                        }
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks() { _ in
                AxisTick()
                AxisValueLabel()
                    .font(.system(size: CustomFontSize.body, weight: .bold, design: .rounded))
            }
        }
        .chartYScale(domain: 0...maxTimeInHours)
        .onAppear {
            Task {
                await loadConfigData()
            }
        }
    }

    // MARK: - Config Management
    @MainActor
    private func loadConfigData() async {
        let configManager = ConfigManager.shared
        personalConfig = configManager.personalConfig
    }
}

// MARK: - Helper Functions
func getDateString(from date: Date, unit: TaskStatisticsNavigator.StatisticsUnit) -> String {
    if unit == .week {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "EEE"
        return formatter.string(from: date)
    } else {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: date)
    }
}

func getDateLocaleDesc(from date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "MMM d"
    return formatter.string(from: date)
}

// 拓展：获取每周的开始日（周一或周日可调）
extension Calendar {
    func startOfWeek(for date: Date) -> Date {
        let components = dateComponents([.yearForWeekOfYear, .weekOfYear], from: date)
        return self.date(from: components)!
    }
    
    func endOfWeek(for date: Date) -> Date {
        let start = startOfWeek(for: date)
        return self.date(byAdding: .day, value: 6, to: start)!
    }
    
    func startOfMonth(for date: Date) -> Date {
        return self.date(from: self.dateComponents([.year, .month], from: date))!
    }
    
    func endOfMonth(for date: Date) -> Date {
        let start = startOfMonth(for: date)
        var components = DateComponents()
        components.month = 1
        components.day = -1
        return self.date(byAdding: components, to: start)!
    }
    
    func startOfYear(for date: Date) -> Date {
        return self.date(from: self.dateComponents([.year], from: date))!
    }
    
    func endOfYear(for date: Date) -> Date {
        let start = startOfYear(for: date)
        var components = DateComponents()
        components.year = 1
        components.day = -1
        return self.date(byAdding: components, to: start)!
    }
}

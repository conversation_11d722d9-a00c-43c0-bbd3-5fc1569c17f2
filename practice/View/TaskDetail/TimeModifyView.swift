//
//  TimeModifyView.swift
//  practice
//
//  Created by 陈昇 on 3/15/24.
//

import SwiftUI
import HorizonCalendar


struct TimeModifyView: View {
    @Environment(\.presentationMode) var presentationMode
    
    private let minuteRange = Array(stride(from: 0, to: 59, by: 5))
    private let hourRange = 0...23
    
    @State private var selectedMinute: Int = 0
    @State private var selectedDate: Date = Date()

    @State private var selectedHour: Int = 0
    @State private var focusedField: FocusField? = nil
    
    enum FocusField {
        case date, time
    }
    
    var onConfirm: (_: Int, _: Date) -> () = {seconds, date in }
    
    var body: some View {
        ZStack {
            VStack {
                Spacer()
                    .frame(height: 100)
                HStack(spacing: 40) {
                    VStack {
                        Text("Date")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                        Text(selectedDate.formatted(.dateTime.year().month().day()))
                            .font(.title3)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(focusedField == .date ? Color.accentColor : Color.gray.opacity(0.3), lineWidth: 1)
                                    .background(
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(Color(.systemBackground))
                                            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                                    )
                            )
                            .onTapGesture {
                                focusedField = .date
                            }
                    }

                    VStack {
                        Text("Practice Time")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                        Text(getTimeString(hour: selectedHour, minute: selectedMinute))
                            .font(.title3)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(focusedField == .time ? Color.accentColor : Color.gray.opacity(0.3), lineWidth: 1)
                                    .background(
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(Color(.systemBackground))
                                            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                                    )
                            )
                            .onTapGesture {
                                focusedField = .time
                            }
                    }
                }
                
                Text("If there is already practice time recorded for this date, the new time will be added to the existing total.")
                    .multilineTextAlignment(.center)
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .padding()

                Divider().padding(.vertical)
                // 根据 focus 展示相应 picker
                VStack {
                    if focusedField == .date {
                        DatePicker("Select Date", selection: $selectedDate, in: ...Date(), displayedComponents: .date)
                            .datePickerStyle(.graphical)
                            .labelsHidden()
                            .transition(.opacity)
                    }
                    
                    if focusedField == .time {
                        HStack {
                            VStack {
                                Text("Hour").bold()
                                    .padding(.top, 20)
                                Picker("", selection: $selectedHour) {
                                    ForEach(hourRange, id: \.self) { hour in
                                        Text("\(hour)").tag(hour) //
                                    }
                                }
                                .pickerStyle(WheelPickerStyle())
                            }
                            
                            VStack {
                                Text("Minute")
                                    .bold()
                                    .padding(.top, 20)
                                Picker("", selection: $selectedMinute) {
                                    ForEach(minuteRange, id: \.self) { minute in
                                        Text("\(minute)").tag(minute)
                                    }
                                }
                                .pickerStyle(WheelPickerStyle())
                            }
                        }
                        .transition(.opacity)
                    }
                    Spacer()
                }
            }
            VStack {
                HStack {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }, label: {
                        Text("Cancel").bold().padding()
                    })
                    Spacer()
                    Button(action: {
                        onConfirm(getSeconds(), selectedDate)
                    }, label: {
                        Text("Confirm").bold().padding()
                    })
                    .disabled(getSeconds() == 0)
                }
                Spacer()
            }
        }
    }
    
    
    func getTimeString(hour: Int, minute: Int) -> String {
        let components = DateComponents(hour: hour, minute: minute)
        let date = Calendar.current.date(from: components) ?? Date()
        return date.formatted(date: .omitted, time: .shortened)
    }
    
    private func getSeconds () -> Int {
        return self.selectedHour * 3600 + self.selectedMinute * 60
    }
}


//
//  InfoCardTimeView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/4/6.
//

import SwiftUI

struct InfoCardTimeView: View {
    enum TimeType {
        case today
        case total
    }
    
    let task: TaskItem
    var timeType: TimeType = TimeType.total
    
    var body: some View {
        VStack {
            HStack(alignment: .center, spacing: 8) {
                Text(task.pieceName)
                    .font(.title3)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.leading)
                    .padding(.horizontal)
                Spacer()
            }
            .padding(.top)
            HStack(alignment: .center, spacing: 8) {
                if task.composerName != "" {
                    Text(task.composerName)
                        .foregroundColor(Color(.label).opacity(0.5))
                        .italic()
                        .font(.subheadline)
                        .padding(.horizontal)
                }
                Spacer()
                if timeType == .today {
                    Text("Today: \(getTimeString(of: getPracticeTime(of: task, at: Date())))")
                        .foregroundColor(Color(.label).opacity(0.5))
                        .font(.subheadline)
                        .padding(.horizontal)
                } else {
                    Text("\(getTimeString(of: getPracticeTime(of: task)))")
                        .foregroundColor(Color(.label).opacity(0.5))
                        .font(.subheadline)
                        .padding(.horizontal)
                }
                
            }
            .padding(.bottom)
        }
    }
    
    init(task: TaskItem, timeType: TimeType = .total) {
        self.task = task
        self.timeType = timeType
    }
}

#Preview {
    InfoCardTimeView(
        task: TaskItem(
            pieceName: "Nocturne Op. 09 Nr, 01",
            composerName: "Chopin",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date()
        )
    )
}

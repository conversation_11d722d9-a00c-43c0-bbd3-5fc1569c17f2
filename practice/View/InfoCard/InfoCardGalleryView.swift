//
//  InfoCardGalleryView.swift
//  practice
//
//  Created by 陈昇 on 3/25/24.
//

import SwiftUI

struct InfoCardGalleryView: View {
    let task: TaskItem
    let width: CGFloat
    @EnvironmentObject private var imageManager: ImageManager
    var syncRenderImage = false
    
    var body: some View {
        ZStack {
            Group {
                if let imagePath = task.coverImagePath {
                    if syncRenderImage {
                        if let image = imageManager.loadImage(path: imagePath) {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: width, height: width)
                                .blur(radius: 1)
                                .overlay(content: {
                                    Color(.black)
                                        .opacity(0.3)
                                })
                        }
                    } else {
                        CloudImageView(path: imagePath)
                            .aspectRatio(contentMode: .fill)
                            .frame(width: width, height: width)
                            .blur(radius: 1)
                            .overlay(content: {
                                Color(.black)
                                    .opacity(0.3)
                            })
                    }
                } else {
                    ZStack {
                        // 基础色（比如亨乐蓝）
                        Color(hex: "#6991a5")
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.05),
                                Color.black.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    }
                }
            }
            VStack(alignment: .leading, spacing: 8) {
                Spacer()
                Text(task.pieceName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                if !task.composerName.isEmpty {
                    Text(task.composerName)
                        .font(.body)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .foregroundColor(.white)
            .padding(.horizontal)
            .padding(.bottom)
        }
        .frame(width: width, height: width)
        .clipped()
        .contentShape(Rectangle())
        .cornerRadius(6)
    }
}

#Preview {
    InfoCardGalleryView(
        task: TaskItem(
            pieceName: "Op",
            composerName: "Chopin",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date()
        ),
        width: 200
    )
    InfoCardGalleryView(
        task: TaskItem(
            pieceName: "Nocturne Op. 09 Nr, 01",
            composerName: "",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date()
        ),
        width: 200
    )
}

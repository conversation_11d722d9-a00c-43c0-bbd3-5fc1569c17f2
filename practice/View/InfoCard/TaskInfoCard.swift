//
//  TaskInfoCard.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI

struct TaskInfoCard: View {
    let task: TaskItem
    
    var body: some View {
        HStack(alignment: .center , spacing: 0) {
            let (time, _) = getTimeStringPair(of: getPracticeTime(of: task), unit: .hour)
            let timeCellWidth = 100.0
            VStack(alignment: .leading) {
                HStack(alignment: .firstTextBaseline, spacing: 0) {
                    Text(task.pieceName)
                        .font(.title2)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                }
                if let recordingCount = task.recordings?.count, recordingCount > 0 {
                    HStack(spacing: 5) {
                        Image(systemName: "microphone.fill")
                        Text("\(recordingCount)")
                    }
                    .font(.body)
                    .fontWeight(.bold)
                    .foregroundStyle(.gray)
                }
            }
            Spacer()
            VStack(alignment: .center, spacing: 0) {
                Text("\(time)")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .minimumScaleFactor(0.5)
                    .foregroundColor(Color(.accent))
                    .frame(width: timeCellWidth)
                    .lineLimit(1)
                Text("hours")
                    .font(.title3)
                    .fontWeight(.bold)
                    .frame(width: timeCellWidth)
                    .foregroundColor(Color(.accent))
            }
        }
        .padding(.leading)
        .padding(.vertical)
    }
    
    init(task: TaskItem) {
        self.task = task
    }
}

#Preview {
    TaskInfoCard(
        task: TaskItem(
            pieceName: "Nocturne Op. 09 Nr, 01Nocturne Op. 09 Nr, 01Nocturne Op. 09 Nr, 01",
            composerName: "Chopin",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date()
        )
    )
    .listCardStyle(backgroundColor: Color(.systemBackground))
    .shadow(color: .primary.opacity(0.1), radius: 10)
    .listRowInsets(EdgeInsets())
    .listRowSeparator(.hidden)
    .padding(.vertical)
}

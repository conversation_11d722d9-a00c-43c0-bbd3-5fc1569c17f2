//
//  InfoCardView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI

struct InfoCardView: View {
    let task: TaskItem
    var syncRenderImage = false
    @EnvironmentObject private var imageManager: ImageManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if let imagePath = task.coverImagePath {
                if syncRenderImage {
                    if let image = imageManager.loadImage(path: imagePath) {
                        Image(uiImage: image)
                            .resizable()
                            .frame(maxWidth: .infinity)
                            .aspectRatio(contentMode: .fit)
                    }
                } else {
                    CloudImageView(path: imagePath)
                        .frame(maxWidth: .infinity)
                        .aspectRatio(contentMode: .fit)
                }
            }
            Text(task.pieceName)
                .font(.title3)
                .fontWeight(.bold)
                .multilineTextAlignment(.leading)
                .padding(.horizontal)
                .padding(.top)
            HStack(alignment: .top) {
                InfoBlock(label: String(localized: "Composer"), value: task.composerName)
                if task.taskType == .Achievement {
                    InfoBlock(label: String(localized: "Finished Date"), value: task.finishedDate.formatted(.iso8601.year().month().day())
                    )
                } else {
                    InfoBlock(label: String(localized: "Start Date"), value: task.beginDate.formatted(.iso8601.year().month().day())
                    )
                }
            }.padding(.horizontal)
            if task.taskType == .Achievement {
                HStack(alignment: .top) {
                    InfoBlock(label: String(localized: "Practice Time"), value: getTimeString(of: getPracticeTime(of: task.taskProgress ?? [])))
                    let duration = getDayDifference(from: task.beginDate, to: task.finishedDate)
                    InfoBlock(label: String(localized: "Total Duration"), value: String(localized: "\(duration) days"))
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
        }
        .listCardStyle(backgroundColor: Color(.secondarySystemFill).opacity(0.5))
    }
}

enum InfoCardViewType: String, Identifiable {
    case normal = "normal"
    case compact = "compact"
    case gallery = "gallery"
    case album = "album"
    
    var id: String { self.rawValue }
}

let cardViewType: [InfoCardViewType] = [.compact, .gallery, .normal, .album]

let cardViewIconMap: [InfoCardViewType: String] = [
    .compact: "rectangle.grid.1x2.fill",
    .gallery: "rectangle.inset.filled",
    .normal: "rectangle.tophalf.filled",
    .album: "photo.stack"
]

struct ListCardView: ViewModifier {
    var backgroundColor: Color
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: /*@START_MENU_TOKEN@*/.infinity/*@END_MENU_TOKEN@*/)
            .background(backgroundColor)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(.accent, lineWidth: 0)
            )
    }
}

extension View {
    func listCardStyle(backgroundColor: Color) -> some View {
        modifier(ListCardView(backgroundColor: backgroundColor))
    }
}


#Preview {
    InfoCardView(
        task: TaskItem(
            pieceName: "Nocturne Op. 09 Nr, 01",
            composerName: "Chopin",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date(),
            coverImagePath: "123123"
        )
    )
}

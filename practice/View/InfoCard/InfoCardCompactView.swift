//
//  InfoCardCompactView.swift
//  practice
//
//  Created by 陈昇 on 3/25/24.
//

import SwiftUI

struct InfoCardCompactView: View {    
    let task: TaskItem
    @EnvironmentObject private var imageManager: ImageManager
    var syncRenderImage = false

    var body: some View {
        ZStack {
            VStack(alignment: .leading, spacing: 8) {
                Text(task.pieceName)
                    .foregroundColor(Color(.white))
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.leading)
                    .padding(.horizontal)
                    .padding(.top)
                HStack {
                    Text(task.composerName)
                        .foregroundColor(Color(.white))
                        .font(.body)
                        .italic()
                        .multilineTextAlignment(.leading)
                    Spacer()
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .frame(maxWidth: /*@START_MENU_TOKEN@*/.infinity/*@END_MENU_TOKEN@*/)
            .background {
                if let imagePath = task.coverImagePath {
                    if syncRenderImage {
                        if let image = imageManager.loadImage(path: imagePath) {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .blur(radius: 1)
                                .overlay(content: {
                                    Color(.black).opacity(0.3)
                                })
                        }
                    } else {
                        CloudImageView(path: imagePath)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .aspectRatio(contentMode: .fill)
                            .blur(radius: 1)
                            .overlay(content: {
                                Color(.black).opacity(0.3)
                            })
                    }
                } else {
                    ZStack {
                        // 基础色（比如亨乐蓝）
                        Color(hex: "#6991a5")
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.05),
                                Color.black.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    }
                }
            }
            .clipped()
            .contentShape(Rectangle())
        }
        .bold()
        .cornerRadius(10)
    }
}

#Preview {
    InfoCardCompactView(
        task: TaskItem(
            pieceName: "Nocturne Op. 09 Nr, 01",
            composerName: "Chopin",
            key: .aMinor,
            difficulty: .nine,
            beginDate: Date()
        )
    )
}

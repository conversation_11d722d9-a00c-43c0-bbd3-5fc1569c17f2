//
//  TaskCardView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/12.
//

import SwiftUI

struct TaskCardView: View {
    let task: TaskItem
    let width: CGFloat
    @EnvironmentObject private var imageManager: ImageManager
    var syncRenderImage = false

    var body: some View {
        VStack(spacing: 8) {
            // Cover Image (1:1 aspect ratio)
            coverImageView
                .shadow(color: .primary.opacity(0.3), radius: 3, x: 0, y: 0)
            // Title Section
            titleView
        }
        .frame(width: width)
        .cornerRadius(8)
        .contentShape(Rectangle())
    }
    
    private var coverImageView: some View {
        Group {
            if let imagePath = task.coverImagePath {
                if syncRenderImage {
                    if let image = imageManager.loadImage(path: imagePath) {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: width, height: width)
                            .clipped()
                    }
                } else {
                    CloudImageView(path: imagePath)
                        .aspectRatio(contentMode: .fill)
                        .frame(width: width, height: width)
                        .clipped()
                }
            } else {
                ZStack {
                    Color(.accent)
                        .opacity(0.8)

                    VStack(spacing: 4) {
                        Image(systemName: "music.note")
                            .font(.largeTitle)
                            .fontWeight(.black)
                            .foregroundColor(.white)
                    }
                }
                .frame(width: width, height: width)
            }
        }
        .cornerRadius(6)
    }

    private var titleView: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(task.pieceName)
                .font(.footnote)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(2)
                .truncationMode(.tail)
                .multilineTextAlignment(.leading)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, 4)
        .padding(.bottom, 8)
    }
}

#Preview {
    HStack(spacing: 12) {
        // Task with cover image
        TaskCardView(
            task: TaskItem(
                pieceName: "Nocturne Op. 9 No. 1 in E-flat Major",
                composerName: "Chopin",
                key: .eFlatMajor,
                difficulty: .five,
                beginDate: Date()
            ),
            width: 120
        )

        // Task without cover image
        TaskCardView(
            task: TaskItem(
                pieceName: "Piano Sonata No. 14 in C-sharp minor 'Moonlight Sonata'",
                composerName: "Ludwig van Beethoven",
                key: .cSharpMinor,
                difficulty: .seven,
                beginDate: Date()
            ),
            width: 120
        )

        // Task with very long title to test truncation
        TaskCardView(
            task: TaskItem(
                pieceName: "Fantaisie-Impromptu in C-sharp minor, Op. 66",
                composerName: "Frédéric François Chopin",
                key: .cSharpMinor,
                difficulty: .eight,
                beginDate: Date()
            ),
            width: 120
        )
    }
    .padding()
    .environmentObject(ImageManager(modelContext: TaskItemSchemaV1.container.mainContext))
}

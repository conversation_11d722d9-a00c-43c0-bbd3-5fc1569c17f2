//
//  TodayProgressView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI

struct TodayProgressView: View {
    var body: some View {
        ZStack {
//            VStack {
//                let (time, unit) = getTimeStringPair(of: todayPracticeTime, unit: .minute)
//                Text("\(time)")
//                    .font(.system(size: 100))
//                Text("\(unit)")
//            }
//            .bold()
//            progressView
//                .frame(width: width, height: width / 2)
        }
//        .frame(maxHeight: width / 2)
    }
}

#Preview {
    TodayProgressView()
}

//
//  PracticeView.swift
//  practice
//
//  Created by 陈昇 on 4/10/24.
//

import SwiftUI
import SwiftData
import ConfettiSwiftUI
import FirebaseAnalytics

struct PracticeView: View {
    //    MARK: property
    @StateObject private var configManager: ConfigManager = .shared
    @StateObject private var appGlobalStateManager: AppGlobalStateManager = .shared
    @Environment(\.scenePhase) private var scenePhase

    @SceneStorage("PracticeView.selectedTask") var selectedTaskId: String?
    @SceneStorage("PracticeView.showCountDownPage") private var showCountDownPage = false
    
    // 录音属性
    @State private var showRecordingPage = false
    @EnvironmentObject private var recordingManager: RecordingManager
    @StateObject private var taskListManager: TaskListManager = .shared
    @State private var showCustomAlert = false
    
    @State var showTaskSelectModal = false
    
    @State private var expandTaskList = true
    @State private var expandAchievementList = true
    @State private var expandDefaultList = false
    @State private var showConfetti = 0
    @State private var todayPracticeProcess: CGFloat = 0.0
    @State private var todayPracticeTimeMinuteNumber: Int = 0
    @State private var showStreakCalendarView = false
    @Namespace private var animationNamespace
    @SceneStorage("BottomTabView.selectedTab") private var selectedTab = "Practice"
    
    private var defaultTask: TaskItem? {
        taskListManager.getDefaultTask()
    }
    
    var isPad: Bool {
        UIDevice.current.userInterfaceIdiom == .pad
    }
    
    private var selectedTask: TaskItem? {
        if let id = selectedTaskId {
            return taskListManager.getTaskById(id: id)
        }
        return nil
    }
    
    var onGoingItems: [TaskItem] {
        taskListManager.getOngoingAndDefaultTasks()
    }
    
    var achievementItems: [TaskItem] {
        taskListManager.getAchievementTask()
    }
    
    private var todayPracticeTime: Int {
        getPracticeTime(of: taskListManager.taskItems, at: Date())
    }
    
    private var todayLeftTime: Int {
        let goalTime = configManager.getDailyGoal() * 60
        if goalTime > todayPracticeTime {
            return (goalTime - todayPracticeTime) / 60
        } else {
            return 0
        }
    }
    
    typealias CountDownStatus = CountDownContainer.CountDownStatus
    
    //    MARK: View Component
    var body: some View {
        NavigationStack {
            GeometryReader { proxy in
                let contentWidth = proxy.size.width
                ZStack {
                    VStack(spacing: 0) {
                        StreakCardView(width: contentWidth)
                            .padding(.horizontal)
                            .onTapGesture {
                                withAnimation(.spring()) {
                                    showStreakCalendarView = true
                                }
                            }
                        HStack(spacing: 0) {
                            if todayLeftTime > 0 {
                                Text("Today's practicing \(todayLeftTime) minutes left")
                            } else {
                                Text("Today's practicing finished")
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.accentColor)
                                    .padding(.leading, 10)
                            }
                            Spacer()
                        }
                        .font(.custom("Nunito-Bold", size: 20))
                        .foregroundStyle(.primary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        todayProgressView
                        Spacer()
                            .frame(minHeight: 0)
                        taskSelector
                    }
                }
                .background(Color(.secondarySystemBackground))
                .navigationTitle("Practice")
            }
        }
        .sheet(isPresented: $showStreakCalendarView, content: {
            GeometryReader { proxy in
                let contentWidth = proxy.size.width
                StreakCalendarView(width: contentWidth)
            }
        })
        .fullScreenCover(isPresented: $showCountDownPage, content: {
            countDownPopUpView
        })
        .fullScreenCover(isPresented: $showRecordingPage) {
            if let task = selectedTask {
                RecordingView(task: task)
            }
        }
        .overlay {
            if showCustomAlert {
                CustomAlertView(
                    title: String(localized: "Unable to Record"),
                    message: String(localized: "Recording is not supported for the default task. Please select your own task or create a new one and try again."),
                    onDismiss: {
                        withAnimation(.easeInOut) {
                            showCustomAlert = false
                        }
                    }
                )
                .transition(.opacity)
                .animation(.easeInOut, value: showCustomAlert)
            }
        }
        .onAppear {
            Analytics.logEvent("page_show", parameters: [
                AnalyticsParameterItemID: "practice-page",
                AnalyticsParameterItemName: "practice-page",
            ])
        }
        .onChange(of: taskListManager.taskProgress.map { $0.practiceTime }, initial: true) {
            withAnimation {
                todayPracticeTimeMinuteNumber = getPracticeTime(of: taskListManager.taskItems, at: Date()) / 60
                updateTodayPracticeProcess()
            }
        }
        .onChange(of: todayPracticeTimeMinuteNumber) { oldVal, newVal in
            let goalTime = configManager.getDailyGoal()
            if oldVal < goalTime && newVal >= goalTime {
                showConfetti += 1
                HapticManager.lightImpact()
                print("should show confetti")
            }
        }
        .onChange(of: onGoingItems, initial: true) {
            if selectedTask == nil, let taskItem = onGoingItems.first {
                selectedTaskId = taskItem.id.uuidString
            }
        }
        .onChange(of: scenePhase) { oldPhase, newPhase in
            print("on scenePhase change")
            if oldPhase != .active && newPhase == .active {
                withAnimation {
                    todayPracticeTimeMinuteNumber = getPracticeTime(of: taskListManager.taskItems, at: Date()) / 60
                    updateTodayPracticeProcess()
                }
            }
        }
        .confettiCannon(counter: $showConfetti, num: 100)
    }
    
    private var todayProgressView: some View {
        progressView
            .overlay (
                VStack {
                    Spacer()
                    Text("\(todayPracticeTimeMinuteNumber)")
                        .font(.system(size: 40, weight: .bold, design: .rounded))
                        .minimumScaleFactor(0.5)
                    Text("minutes")
                        .font(.custom("Nunito-Bold", size: 20))
                        .padding(.bottom, 40)
                }
                .bold()
            )
    }
    
    @ViewBuilder
    var countDownPopUpView: some View {
        if let selectedTask = selectedTask {
            CountDownContainer(
                onEnd: { second in
                    var todayProgress: TaskProgress?
                    let today = Calendar.current.startOfDay(for: Date())
                    if let index = selectedTask.taskProgress?.firstIndex(where: { Calendar.current.isDate($0.date, inSameDayAs: today) }) {
                        todayProgress = selectedTask.taskProgress?[index]
                    }
                    if todayProgress == nil {
                        let progressItem = TaskProgress(
                            date: Date(),
                            status: .finished
                        )
                        selectedTask.taskProgress?.append(progressItem)
                        selectedTask.lastModified = .now
                    }
                    // 延迟 0.3 秒执行（可自定义）
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        taskListManager.updatePracticeTime(of: selectedTask, at: today, by: second)
                    }
                },
                task: selectedTask
            )
        }
    }
    
    @ViewBuilder
    private var taskSelector: some View {
        VStack(spacing: 0) {
            if let selectedTask = selectedTask {
                HStack {
                    Text("\(selectedTask.pieceName)")
                        .font(.largeTitle)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)
                    Image(systemName: "arrowtriangle.down.fill")
                        .font(.body)
                        .foregroundColor(.accentColor.opacity(0.3))
                }
                .bold()
                .foregroundColor(.secondary)
                .padding(.horizontal)
                .padding(.bottom)
                .onTapGesture {
                    showTaskSelectModal = true
                }
                .sheet(isPresented: $showTaskSelectModal, content: {
                    NavigationView {
                        taskSelectorSheet
                            .presentationDetents([.medium, .large])
                            .presentationDragIndicator(.visible)
                            .navigationTitle("Select Task")
                            .toolbarTitleDisplayMode(.inline)
                    }
                })
            }
            footerView
                .padding(.bottom)
        }
    }
    
    @ViewBuilder
    var taskSelectorSheet: some View {
        VStack {
            List {
                Section (
                    isExpanded: $expandTaskList,
                    content: {
                        ForEach(onGoingItems, id: \.self.id) {item in
                            getInfoView(task: item)
                        }
                    },
                    header: {
                        Text("In Progress")
                            .font(.headline)
                            .bold()
                            .textCase(nil)
                    }
                )
                
                Section (
                    isExpanded: $expandAchievementList,
                    content: {
                        ForEach(achievementItems, id: \.self.id) {item in
                            getInfoView(task: item)
                        }
                    },
                    header: {
                        Text("Completed")
                            .font(.headline)
                            .bold()
                            .textCase(nil)
                    })
            }
            .textCase(nil)
            .listStyle(.sidebar)
        }
    }
    
    @ViewBuilder
    var footerView: some View {
        HStack(spacing: 0) {
            if let selectedTask {
                Button(action: {
                    showCountDownPage = true
                }) {
                    Text("Practice")
                        .bold()
                        .font(.largeTitle)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(.accent)
                        .cornerRadius(10)
                        .padding(.leading)
                }
                Button(action: {
                    showRecordingPage = true
                }) {
                    Image(systemName: "mic.fill")
                        .font(.largeTitle)
                        .foregroundColor(Color.accentColor)
                        .padding()
                        .padding(.horizontal, 10)
                        .background(
                            (selectedTask == defaultTask ? Color.gray.opacity(0.6) : Color.accentColor.opacity(0.1))
                        )
                        .cornerRadius(10)
                        .padding(.horizontal)
                        .opacity(selectedTask == defaultTask ? 0.5 : 1.0)
                        .grayscale(selectedTask == defaultTask ? 0.8 : 0)
                }
            } else {
                Button(action: {
                    selectedTab = "CurrentList"
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        appGlobalStateManager.showTaskListCreatNewTaskSheet = true
                    }
                }) {
                    Text("Create New Task")
                        .bold()
                        .font(.largeTitle)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(.accent)
                        .cornerRadius(10)
                        .padding(.horizontal)
                }
            }
        }
    }
    
    @ViewBuilder
    var progressView: some View {
        ArcProgressView(progress: $todayPracticeProcess, color: .accentColor, lineWidth: 12)
            .rotationEffect(.degrees(180))
            .frame(width: 200, height: 200)
    }
    
    private func getInfoView(task: TaskItem) -> some View {
        InfoCardTimeView(task: task, timeType: .today)
            .contentShape(Rectangle())
            .listRowInsets(EdgeInsets())
            .onTapGesture {
                selectedTaskId = task.id.uuidString
                showTaskSelectModal = false
            }
    }
    
    private func updateTodayPracticeProcess() {
        let goalTimeSecond = configManager.getDailyGoal() * 60
        todayPracticeProcess = CGFloat(todayPracticeTime) / CGFloat(goalTimeSecond)
        print("updateTodayPracticeProcess", todayPracticeProcess)
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container
    PracticeView()
        .modelContainer(for: TaskItem.self, inMemory: false)
        .environmentObject(RecordingManager(modelContext: modelContext.mainContext))
}

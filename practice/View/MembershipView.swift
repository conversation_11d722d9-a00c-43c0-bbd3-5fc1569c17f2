//import SwiftUI
//import StoreKit
//
//struct MembershipView: View {
//    @StateObject private var membershipManager = MembershipManager()
//    @State private var isLoading = false
//    
//    var body: some View {
//        NavigationView {
//            List {
//                // 会员状态区域
//                Section {
//                    HStack {
////                        Text("当前会员状态")
//                        Spacer()
//                        Text(membershipStatusText)
//                            .foregroundColor(membershipManager.isPremium ? .green : .gray)
//                            .bold()
//                    }
//                } header: {
//                    Text("会员状态")
//                }
//                
//                // 购买选项区域
//                Section {
//                    ForEach(membershipManager.products, id: \.id) { product in
//                        Button(action: {
//                            purchaseProduct(product)
//                        }) {
//                            HStack {
//                                VStack(alignment: .leading) {
//                                    Text(product.displayName)
//                                        .font(.headline)
//                                    Text(product.description)
//                                        .font(.caption)
//                                        .foregroundColor(.gray)
//                                }
//                                Spacer()
//                                Text(product.displayPrice)
//                                    .bold()
//                            }
//                        }
//                        .disabled(isProductOwned(product) || isLoading)
//                    }
//                } header: {
//                    Text("订阅选项")
//                }
//                
//                // 恢复购买按钮
//                Section {
//                    Button(action: {
//                        restorePurchases()
//                    }) {
//                        HStack {
//                            Spacer()
//                            if isLoading {
//                                ProgressView()
//                                    .padding(.trailing, 5)
//                            }
//                            Text("恢复购买")
//                            Spacer()
//                        }
//                    }
//                    .disabled(isLoading)
//                }
//            }
//            .navigationTitle("会员订阅")
//            .overlay {
//                if membershipManager.products.isEmpty {
//                    VStack {
//                        ProgressView()
//                        Text("正在加载产品信息...")
//                            .padding(.top)
//                    }
//                }
//            }
//        }
//        .task {
//            // 刷新产品信息
//            await membershipManager.loadProducts()
//            // 刷新会员状态
//            await membershipManager.updateMembershipStatus()
//        }
//    }
//    
//    // 会员状态显示文本
//    private var membershipStatusText: String {
//        switch membershipManager.status {
//        case .free:
//            return "免费用户"
//        case .monthly:
//            return "月度会员"
//        case .yearly:
//            return "年度会员"
//        case .lifetime:
//            return "终身会员"
//        }
//    }
//    
//    // 判断产品是否已经拥有
//    private func isProductOwned(_ product: Product) -> Bool {
//        switch product.id {
//        case membershipManager.getProduct(productID: "com.onlypractice.monthly")?.id:
//            return membershipManager.status == .monthly
//        case membershipManager.getProduct(productID: "com.onlypractice.yearly")?.id:
//            return membershipManager.status == .yearly || membershipManager.status == .lifetime
//        case membershipManager.getProduct(productID: "com.onlypractice.lifetime")?.id:
//            return membershipManager.status == .lifetime
//        default:
//            return false
//        }
//    }
//    
//    // 购买产品
//    private func purchaseProduct(_ product: Product) {
//        isLoading = true
//        
//        Task {
//            do {
//                let success = try await membershipManager.purchase(product: product)
//                if success {
//                    print("成功购买: \(product.displayName)")
//                } else {
//                    print("购买失败或被取消: \(product.displayName)")
//                }
//            } catch {
//                print("购买过程中出错: \(error.localizedDescription)")
//            }
//            
//            isLoading = false
//        }
//    }
//    
//    // 恢复购买
//    private func restorePurchases() {
//        isLoading = true
//        
//        Task {
//            await membershipManager.restorePurchases()
//            isLoading = false
//        }
//    }
//} 
//
//#Preview {
//    MembershipView()
//}

//
//  ShareImageFooter.swift
//  practice
//
//  Created by 陈昇 on 4/19/24.
//

import SwiftUI

struct ShareImageFooter: View {
    var body: some View {
        HStack {
            Image("Logo")
                .resizable()
                .scaledToFill()
                .frame(width: 50, height: 50)
                .cornerRadius(4)
            VStack (alignment: .leading, spacing: 0) {
                Text("Only Practice")
                    .bold()
                    .multilineTextAlignment(.leading)
                Spacer()
                Text("Can Make Perfect!")
                    .multilineTextAlignment(.leading)
            }
            Spacer()
            Image("AppStoreIcon")
                .resizable()
                .scaledToFill()
                .frame(width: 50, height: 50)
                .cornerRadius(4)
            Image(
                uiImage: generateQRCode(from: "https://apps.apple.com/us/app/only-practice/id6476144775"))
            .interpolation(.none)
            .resizable()
            .scaledToFill()
            .frame(width: 40, height: 40)
//            .padding(10)
//            .cornerRadius(10)
            .opacity(0.6)
            .shadow(radius: 2)
        }
        .frame(height: 40)
        .padding()
    }
}

#Preview {
    ShareImageFooter()
}

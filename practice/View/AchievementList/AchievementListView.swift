import SwiftUI
import SwiftData


struct CoverData: Identifiable {
    var id = UUID()
    var renderedUIImage: UIImage?
}

struct AchievementListView: View {
    @EnvironmentObject private var taskListManager: TaskListManager
    @EnvironmentObject var navManager: NavigationManager
    @EnvironmentObject var recordingManager: RecordingManager
    @EnvironmentObject var imageManager: ImageManager
    @State private var taskItems: [TaskItem] = []
    @State private var showModal = false
    @State private var selectedItem: TaskItem?
    @State private var showingDeleteAlert = false
    
    @State private var showingShareView = false
    @State private var coverData: CoverData?
    
    @Environment(\.displayScale) var displayScale
    
    @State private var renderedShareImage = Image(systemName: "photo")
    
    @AppStorage("cardView") private var cardView = InfoCardViewType.normal
    
    private func updateTaskItems() {
        taskItems = taskListManager.getAchievementTask()
    }
    
    private let cardViewIconMap: [InfoCardViewType: String] = [
        .compact: "rectangle.grid.1x2.fill",
        .gallery: "rectangle.inset.filled",
        .normal: "rectangle.tophalf.filled"
    ]
    
    var body: some View {
        listView
            .navigationTitle("Achievement")
            .navigationDestination(for: TaskItem.self) { task in
                TaskDetailView(task: task)
            }
            .toolbar {
                ToolbarItem {
                    Button {
                        Task {
                            await MainActor.run {
                                render {
                                    showingShareView = true
                                }
                            }
                        }
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                            .imageScale(.large)
                            .frame(width: 44, height: 44, alignment: .center)
                    }
                }
                
                ToolbarItem()
                {
                    Menu(content: {
                        headerMenu
                    }, label: {
                        Image(systemName: "square.stack.fill")
                            .imageScale(.large)
                            .frame(width: 44, height: 44, alignment: .center)
                    })
                }
            }
            .confirmationDialog(
                "Delete",
                isPresented: $showingDeleteAlert,
                titleVisibility: .visible,
                presenting: selectedItem,
                actions: {item in
                    Button(role: .destructive) {
                        withAnimation {
                            taskListManager.deleteTask(taskItem: item)
                            updateTaskItems()
                            Task {
                                recordingManager.cleanOrphanRecordings()
                            }
                        }
                    } label: {
                        Text("Delete")
                    }
                }, message: { item in
                    Text("Are you sure you want to delete \(item.pieceName)?")
                }
            )
            .sheet(isPresented: $showModal) { sheetView }
            .onAppear() {
                updateTaskItems()
            }
            .fullScreenCover(item: $coverData, onDismiss: {
                print("dismiss")
                coverData = nil
            }) {item in
                if let renderedUIImage = item.renderedUIImage {
                    SharePreviewFullScreenView(image: renderedUIImage)
                } else {
                    ProgressView("Generating preview...")
                }
            }
    }
    
    private var listView: some View {
        List {
            if taskItems.count == 0 {
                emptyGuide
            } else {
                ForEach(taskItems, id: \.self) {task in
                    getInfoView(task: task)
                }
            }
        }
        .listStyle(.inset)
    }
    
    var screenshotView: some View {
        VStack(spacing: 0) {
            Spacer()
            HStack {
                Text("Achievement")
                    .font(.largeTitle)
                    .padding()
                    .bold()
                Spacer()
            }
            ForEach(taskItems, id: \.self) {task in
                InfoCardCompactView(task: task)
                    .padding(.bottom)
            }
            ShareImageFooter()
            Spacer()
            
        }
        .frame(width: UIScreen.main.bounds.width)
        .background(Color(.secondarySystemBackground))
    }
    
    @ViewBuilder
    private var headerMenu: some View {
        Picker("", selection: $cardView) {
            ForEach(cardViewType, id: \.self) {type in
                Image(systemName: cardViewIconMap[type] ?? "rectangle.tophalf.filled")
            }
        }.pickerStyle(.segmented)
    }
    
    private var sheetView: some View {
        NavigationView {
            if selectedItem == nil {
                AddNewItemView(
                    onCreate: {
                        showModal = false
                    }
                ).navigationBarTitleDisplayMode(.inline)
            } else {
                AddNewItemView(
                    taskItem: selectedItem!,
                    onSave: {
                        showModal = false
                    }
                ).navigationBarTitleDisplayMode(.inline)
            }
        }
    }
    
    private func getInfoView(task: TaskItem) -> some View {
        let view: any View = if cardView == .compact {
            InfoCardCompactView(task: task)
        } else {
            InfoCardView(task: task)
        }
        return AnyView(view)
            .listRowInsets(EdgeInsets())
            .listRowSeparator(.hidden)
            .padding(.vertical, 8)
            .onTapGesture {
                navManager.pushState(item: .taskDetailPage(task: task))
            }
            .swipeActions(edge: .trailing) {
                Button {
                    selectedItem = task
                    showModal = true
                } label: {
                    Label("Edit", systemImage: "square.and.pencil")
                }
                .tint(.indigo)
                
                Button {
                    selectedItem = task
                    showingDeleteAlert = true
                } label: {
                    Label("Delete", systemImage: "trash.fill")
                }
                .tint(.red)
            }
    }
    
    private var imageWidth: CGFloat {
        let isIPad = UIDevice.current.userInterfaceIdiom == .pad
        let imageWidth = min(UIScreen.main.bounds.width, UIScreen.main.bounds.height)
        if isIPad {
            return imageWidth * 0.5
        } else {
            return imageWidth * 0.8
        }
    }
    
    private var emptyGuide: some View {
        VStack {
            Image("AchievementPlaceholder")
                .resizable()
                .aspectRatio(contentMode: /*@START_MENU_TOKEN@*/.fill/*@END_MENU_TOKEN@*/)
                .frame(width: imageWidth)
                .padding(.bottom, 40)
            HStack {
                Text("Keep practicing and make an achievement")
            }
            .foregroundColor(.gray)
            .font(.body)
            .bold()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .listRowSeparator(.hidden)
    }
    
    @MainActor
    func render(completion: @escaping () -> Void) {
        let latestItems = taskListManager.getAchievementTask()

        let renderView = VStack {
            Text("Achievement").font(.title).bold()
            ForEach(latestItems, id: \.self) { task in
                InfoCardCompactView(task: task, syncRenderImage: true)
            }
            ShareImageFooter()
        }
        .padding()
        .background(Color.white)
        .environmentObject(imageManager)

        let renderer = ImageRenderer(content: renderView)
        renderer.scale = displayScale

        if let uiImage = renderer.uiImage {
            coverData = CoverData(renderedUIImage: uiImage)
            print("✅ render success: \(uiImage.size)")
            completion()
        } else {
            print("❌ render failed: rendered image is nil")
        }
    }
}

#Preview {
    AchievementListView()
        .modelContainer(for: TaskItem.self, inMemory: false)
}

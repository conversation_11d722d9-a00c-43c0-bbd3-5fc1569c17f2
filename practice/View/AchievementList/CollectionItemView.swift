//
//  CollectionItemView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/12.
//

import SwiftUI

struct CollectionItemView: View {
    let collection: CollectionItem
    @EnvironmentObject private var navManager: NavigationManager


    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Collection Title - Tappable to navigate to CollectionDetail
            titleView

            // Horizontal ScrollView of TaskItems
            itemsScrollView
        }
        .padding(.vertical, 8)
    }

    private var titleView: some View {
        VStack(alignment: .leading) {
            HStack {
                Text(collection.name)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Image(systemName: "chevron.right")
                    .font(.callout)
                    .fontWeight(.black)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            .contentShape(Rectangle())
            .onTapGesture {
                navManager.pushState(item: .collectionDetailPage(collection: collection))
            }
            Text("\(collection.achievementCount) achievements included")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .padding(.horizontal)
        }
    }

    private var itemsScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(alignment: .top, spacing: 12) {
                if let taskItems = collection.taskItems, !taskItems.isEmpty {
                    ForEach(taskItems, id: \.id) { task in
                        TaskCardView(task: task, width: 120)
                            .onTapGesture {
                                navManager.pushState(item: .taskDetailPage(task: task))
                            }
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

#Preview {
    let sampleCollection = CollectionItem(name: "Classical Pieces")
    sampleCollection.taskItems = [
        TaskItem(pieceName: "Nocturne Op. 9 No. 1", composerName: "Chopin", key: .eFlatMajor, difficulty: .five, beginDate: Date()),
        TaskItem(pieceName: "Moonlight", composerName: "Beethoven", key: .cSharpMinor, difficulty: .seven, beginDate: Date())
    ]

    sampleCollection.taskItems = []


    return CollectionItemView(collection: sampleCollection)
        .environmentObject(NavigationManager.shared)
}

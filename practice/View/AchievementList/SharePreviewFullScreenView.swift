//
//  SharePreviewFullScreenView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/26.
//
import SwiftUI
import Photos

struct SharePreviewFullScreenView: View {
    @Environment(\.dismiss) var dismiss
    let image: UIImage
    
    @State private var showSaveSuccessAlert = false

    var body: some View {
        ZStack(alignment: .topTrailing) {
            VStack {
                Spacer()
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .padding()
                Spacer()
                
                Button {
                    saveImage()
                } label: {
                    Label("Save to Photos", systemImage: "square.and.arrow.down")
                        .font(.headline)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                        .padding(.horizontal)
                }
                .padding(.bottom, 30)
            }
            .overlay {
                headerView
                    .safeAreaPadding(.all)
                    .padding(.top)
            }
            .safeAreaPadding(.top)
        }
        .background(Color(.systemBackground))
        .edgesIgnoringSafeArea(.all)
        .padding(.bottom, -10)
        .overlay {
            if showSaveSuccessAlert {
                CustomAlertView(
                    title: String(localized: "Saved"),
                    message: String(localized: "The image has been saved to your Photos."),
                    onDismiss: {
                        withAnimation(.easeInOut) {
                            showSaveSuccessAlert = false
                            dismiss()
                        }
                    }
                )
                .transition(.opacity)
                .animation(.easeInOut, value: showSaveSuccessAlert)
            }
        }
    }
    
    func saveImage() {
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { success, error in
            DispatchQueue.main.async {
                if success {
                    showSaveSuccessAlert = true
                }
            }
        }
    }
    
    var headerView: some View {
        VStack {
            HStack {
                Spacer()
                Button(action: {
                    dismiss()
                }, label: {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .bold()
                }).padding()
            }
            Spacer()
        }
    }
}

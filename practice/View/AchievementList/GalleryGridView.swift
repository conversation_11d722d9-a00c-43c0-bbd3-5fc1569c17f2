//
//  GalleryGridView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/23.
//

import SwiftUI

struct GalleryGridView: View {
    let tasks: [TaskItem]
    let onTaskTap: (TaskItem) -> Void
    let onTaskEdit: (TaskItem) -> Void
    let onTaskRemove: ((TaskItem) -> Void)?
    
    // Calculate adaptive columns based on screen width
    private var adaptiveColumns: [GridItem] {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // Total horizontal padding (16 on each side)
        let spacing: CGFloat = 12 // Spacing between items
        let minItemWidth: CGFloat = 300
        let maxItemWidth: CGFloat = 600

        // Calculate available width for content
        let availableWidth = screenWidth - padding

        // Calculate optimal number of columns
        let maxColumns = Int(availableWidth / minItemWidth)
        let minColumns = max(1, Int(availableWidth / maxItemWidth))

        // Choose the number of columns that gives us the best item width
        var optimalColumns = maxColumns
        for columns in minColumns...maxColumns {
            let itemWidth = (availableWidth - CGFloat(columns - 1) * spacing) / CGFloat(columns)
            if itemWidth >= minItemWidth && itemWidth <= maxItemWidth {
                optimalColumns = columns
                break
            }
        }

        // Ensure we have at least 1 column
        optimalColumns = max(1, optimalColumns)

        // Calculate the actual item width
        let actualItemWidth = (availableWidth - CGFloat(optimalColumns - 1) * spacing) / CGFloat(optimalColumns)

        return Array(repeating: GridItem(.fixed(actualItemWidth), spacing: spacing, alignment: .top), count: optimalColumns)
    }
    
    var body: some View {
        LazyVGrid(columns: adaptiveColumns, spacing: 12) {
            ForEach(tasks, id: \.id) { task in
                InfoCardGalleryView(task: task, width: itemWidth)
                    .onTapGesture {
                        onTaskTap(task)
                    }
                    .taskContextMenu(
                        task: task,
                        onEdit: onTaskEdit,
                        onRemove: onTaskRemove
                    )
            }
        }
        .padding(.horizontal, 16)
    }

    // Calculate the item width based on current screen size
    private var itemWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // Total horizontal padding
        let spacing: CGFloat = 12 // Spacing between items
        let minItemWidth: CGFloat = 300
        let maxItemWidth: CGFloat = 600

        let availableWidth = screenWidth - padding
        let maxColumns = Int(availableWidth / minItemWidth)
        let minColumns = max(1, Int(availableWidth / maxItemWidth))

        var optimalColumns = maxColumns
        for columns in minColumns...maxColumns {
            let itemWidth = (availableWidth - CGFloat(columns - 1) * spacing) / CGFloat(columns)
            if itemWidth >= minItemWidth && itemWidth <= maxItemWidth {
                optimalColumns = columns
                break
            }
        }

        optimalColumns = max(1, optimalColumns)
        return (availableWidth - CGFloat(optimalColumns - 1) * spacing) / CGFloat(optimalColumns)
    }
}

#Preview {
    let sampleTasks = [
        TaskItem(pieceName: "Nocturne Op. 9 No. 1", composerName: "Chopin", key: .eFlatMajor, difficulty: .five, beginDate: Date()),
        TaskItem(pieceName: "Moonlight Sonata", composerName: "Beethoven", key: .cSharpMinor, difficulty: .seven, beginDate: Date()),
        TaskItem(pieceName: "Fantaisie-Impromptu", composerName: "Chopin", key: .cSharpMinor, difficulty: .eight, beginDate: Date()),
        TaskItem(pieceName: "Für Elise", composerName: "Beethoven", key: .aMinor, difficulty: .three, beginDate: Date()),
        TaskItem(pieceName: "Prelude in C Major", composerName: "Bach", key: .cMajor, difficulty: .four, beginDate: Date()),
        TaskItem(pieceName: "Clair de Lune", composerName: "Debussy", key: .dFlatMajor, difficulty: .six, beginDate: Date())
    ]

    return ScrollView {
        GalleryGridView(
            tasks: sampleTasks,
            onTaskTap: { task in
                print("Tapped: \(task.pieceName)")
            },
            onTaskEdit: { task in
                print("Edit: \(task.pieceName)")
            },
            onTaskRemove: { task in
                print("Remove: \(task.pieceName)")
            }
        )
    }
    .environmentObject(ImageManager(modelContext: TaskItemSchemaV1.container.mainContext))
}

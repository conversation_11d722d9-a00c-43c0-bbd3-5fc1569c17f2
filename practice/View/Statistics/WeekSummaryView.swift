//
//  WeekSummaryView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/21.
//

import SwiftUI
import SwiftData
import Charts

struct WeekSummaryView: View {
    @State private var selectedWeek: Date = Calendar.current.startOfWeek(for: Date())
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var taskListManager: TaskListManager

    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?

    // Data state variables
    @State private var dailyPracticeTimeData: [(Date, Int)] = []
    @State private var weekPracticeListData: [(String, Int)] = []
    
    // Get the first day of the selected week
    private var weekStartDate: Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: selectedWeek)
        return calendar.date(from: components) ?? Date()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            WeekCalendarPicker(selectedWeekStart: $selectedWeek)
            
            List {
                Section {
                    weekDailyBreakdownView
                        .listRowSeparator(.hidden)
                        .padding(.vertical)
                }
                
                Section {
                    if weekPracticeListData.isEmpty {
                        EmptyStateView(
                            imageName: "CatPiano",
                            title: String(localized: "No data yet"),
                            subtitle: String(localized: "Looks like there's no practice recorded during this time")
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        PracticeSummaryView(list: weekPracticeListData)
                            .listRowSeparator(.hidden)
                    }
                }
            }
            .listStyle(.insetGrouped)
        }
        .background(Color(.secondarySystemBackground))
        .navigationTitle("Week Summary")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadWeekData()
        }
        .onChange(of: selectedWeek) {
            selectedElement = nil
            selectedDate = nil
            loadWeekData()
        }
    }
    
    private var isCurrentWeek: Bool {
        let calendar = Calendar.current
        let currentWeekComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: Date())
        let selectedWeekComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: selectedWeek)
        
        return currentWeekComponents.yearForWeekOfYear == selectedWeekComponents.yearForWeekOfYear &&
               currentWeekComponents.weekOfYear == selectedWeekComponents.weekOfYear
    }
    
    @ViewBuilder
    @MainActor
    private var weekDailyBreakdownView: some View {
        VStack(alignment: .leading, spacing: 10) {
            WeekDailyChartContainerView(
                data: dailyPracticeTimeData,
                selectedDate: $selectedDate,
                selectedElement: $selectedElement
            )
        }
    }

    private func loadWeekData() {
        Task {
            dailyPracticeTimeData = await taskListManager.getDailyPracticeTimeForWeek(weekStartDate)
            weekPracticeListData = await taskListManager.getPracticeListForWeek(weekStartDate)
        }
    }
}

// For handling empty state
struct EmptyPracticeSummaryView: View {
    var message: String = "No practice data yet!"
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
//            Text("\(getTimeString(of: 0))")
//                .font(.title3)
//                .fontWeight(.bold)
//                .foregroundColor(.gray)
//                .padding(.bottom)
            
            Text(message)
                .foregroundColor(.gray)
                .padding(.vertical)
        }
    }
}

struct WeekDailyChartContainerView: View {
    let data: [(Date, Int)]
    @Binding var selectedDate: Date?
    @Binding var selectedElement: (Date, Int)?
    
    var body: some View {
        VStack(alignment: .leading) {
            StatisticsSummaryView(practiceTimeList: data)
                .padding(.bottom)
            
            ChartView(
                data: data,
                unit: TaskStatisticsNavigator.StatisticsUnit.week,
                selectedDate: selectedDate,
                selectedElement: selectedElement,
                showTarget: true
            )
            .frame(height: 180)
            .chartXSelection(value: $selectedDate)
            .onChange(of: selectedDate) {
                if let date = selectedDate {
                    selectedElement = data.first(where: { Calendar.current.isDate($0.0, inSameDayAs: date) })
                }
            }
        }
    }
}

#Preview {
    EmptyPracticeSummaryView()
}

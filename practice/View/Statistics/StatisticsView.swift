//
//  StatisticsView.swift
//  practice
//
//  Created by 陈昇 on 5/20/24.
//

import SwiftUI
import SwiftData
import Charts

struct StatisticsView: View {
    @EnvironmentObject private var navManager: NavigationManager

    @StateObject private var taskListManager: TaskListManager = .shared

    // MARK: data to display
    @State private var totalPracticeTime = 0
    @State private var todayPracticePieceList:  [(String, Int)] = []
    @State private var weekPracticeDateTimeList: [(Date, Int)] = []
    @State private var dailyPracticeTimeForMonth: [(Date, Int)] = []
    @State private var monthlyPracticeTimeForYear: [(Date, Int)] = []

    // MARK: Config data
    @State private var personalConfig: PersonalConfig?

    private var currentWeekStart: Date = Calendar.current.startOfWeek(for: Date())
    
    var body: some View {
        List {
            Section (header: NavigationSectionHeader(title: String(localized: "Total Practice Time"))) {
                let times = getTimeStringPair(of: totalPracticeTime, unit: .hour, decimal: 0)
                LaurelTimeView(
                    timeValue: times.time,
                    timeUnit: times.unit
                )
                .padding(.vertical)
            }
            
            Section(header: NavigationSectionHeader(title: String(localized: "Today Summary"), action: {
                navManager.pushState(item: .daySummaryPage)
            })) {
                PracticeSummaryView(list: todayPracticePieceList)
                    .listRowSeparator(.hidden)
            }
            
            Section(header: NavigationSectionHeader(title: String(localized: "Week Summary"), action: {
                navManager.pushState(item: .weekSummaryPage)
            })) {
                WeekTableView(list: weekPracticeDateTimeList)
                    .listRowSeparator(.hidden)
            }
            
            Section(header: NavigationSectionHeader(title: String(localized: "Month Summary"), action: {
                navManager.pushState(item: .monthSummaryPage)
            })) {
                MonthTableView(list: dailyPracticeTimeForMonth)
                    .listRowSeparator(.hidden)
            }
            
            Section(header: NavigationSectionHeader(title: String(localized: "Year Summary"), action: {
                navManager.pushState(item: .yearSummaryPage)
            })) {
                YearTableView(list: monthlyPracticeTimeForYear)
                .listRowSeparator(.hidden)

            }
        }
        .listStyle(.insetGrouped)
        .listSectionSpacing(0)
        .onAppear {
            Task {
                await loadData()
            }
        }
        .navigationTitle("Statistics")
        .navigationBarTitleDisplayMode(.inline)
    }

    // MARK: - Data Loading
    @MainActor
    private func loadData() async {
        // Load statistics data
        totalPracticeTime = await taskListManager.getPracticeTime()
        todayPracticePieceList = await taskListManager.getTodayPracticePieceList()
        weekPracticeDateTimeList = await taskListManager.getWeekPracticeDateTimeList(currentWeekStart)
        dailyPracticeTimeForMonth = await taskListManager.getDailyPracticeTimeForMonth(Date())
        monthlyPracticeTimeForYear = await taskListManager.getMonthlyPracticeTimeForYear(Date())

        // Load config data
        await loadConfigData()
    }

    @MainActor
    private func loadConfigData() async {
        let configManager = ConfigManager.shared
        personalConfig = configManager.personalConfig
    }
}

// MARK: - Reusable Practice Summary Component
struct PracticeSummaryView: View {
    var height: CGFloat = 50
    var list: [(String, Int)]
    
    var body: some View {
        let sortedList = list.sorted { $0.1 > $1.1 }
        let top3Names = Set(sortedList.prefix(3).map { $0.0 })
        let practiceTime = sortedList.reduce(0) { $0 + $1.1 }
        
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .lastTextBaseline, spacing: 5) {
                let (time, unit) = getTimeStringPair(of: practiceTime, decimal: 1)
                Spacer()
                Text("\(time)")
                    .roundedLargeTitle()
                Text("\(unit)")
                    .roundedBody()
                    .foregroundColor(.gray)
            }
            
            Chart(sortedList, id: \.0) { element in
                BarMark(
                    x: .value("practice time", CGFloat(element.1) / 3600),
                    y: .value("piece name", element.0),
                    width: 15,
                    height: 15
                )
                .cornerRadius(3.0)
                .annotation(position: .top, alignment: .leading, spacing: 0, content: {
                    HStack(alignment: .firstTextBaseline, spacing: 5) {
                        let (time, unit) = getTimeStringPair(of: element.1, decimal: 1)
                        Text(time)
                            .roundedTitle()
                            .fontWeight(.bold)
                            .foregroundColor(.gray)
                            .lineLimit(1)
                            .layoutPriority(1)
                        
                        Text(unit)
                            .roundedBody()
                            .foregroundColor(.gray)
                            .padding(.trailing)
                            .lineLimit(1)
                            .layoutPriority(1)
                        
                        Text(element.0)
                            .roundedBody()
                            .foregroundColor(.gray)
                        
                        Spacer()
                    }
                    .frame(maxWidth: UIScreen.main.bounds.width - 80)
                })
                .foregroundStyle(top3Names.contains(element.0) ? .accent : .gray)
            }
            .chartYAxis(.hidden)
            .chartXAxis(.hidden)
            .chartLegend(.hidden)
            .frame(height: CGFloat(list.count) * height)
            .padding(.top)
        }
    }
}

struct WeekTableView: View {
    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?
    var list: [(Date, Int)]
    
    var body: some View {
        WeekDailyChartContainerView(
            data: list,
            selectedDate: $selectedDate,
            selectedElement: $selectedElement
        )
    }
}

struct MonthTableView: View {
    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?
    var list: [(Date, Int)]
    
    var body: some View {
        MonthDailyChartContainerView(
            data: list,
            selectedDate: $selectedDate,
            selectedElement: $selectedElement
        )
    }
}

struct YearTableView: View {
    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?
    var list: [(Date, Int)]
    
    var body: some View {
        YearMonthlyChartContainerView(
            data: list,
            selectedDate: $selectedDate,
            selectedElement: $selectedElement
        )
    }
}

// Custom section header with navigation action
struct NavigationSectionHeader: View {
    let title: String
    let action: (() -> Void)?

    init(title: String, action: (() -> Void)? = nil) {
        self.title = title
        self.action = action
    }

    @ViewBuilder
    var body: some View {
        if let action = action {
            Button(action: action) {
                content(showChevron: true)
            }
            .buttonStyle(.plain)
            .contentShape(Rectangle())
        } else {
            content(showChevron: false)
        }
    }

    // 抽出公用的 HStack
    @ViewBuilder
    private func content(showChevron: Bool) -> some View {
        HStack {
            Text(title)
                .font(.headline)
            Spacer()
            if showChevron {
                Image(systemName: "chevron.right")
                    .font(.caption)
            }
        }
        .foregroundColor(.primary)
    }
}

//enum StatisticsUnit: String, Identifiable, CaseIterable {
//    case sevenDays = "7D"
//    case fourteenDays = "14D"
//    case thirtyDays = "30D"
//    case halfYears = "6M"
//    case oneYear = "12M"
//    
//    var id: String { self.rawValue }
//}

#Preview {
    StatisticsView()
}

//
//  YearSummaryView.swift
//  practice
//
//  Created by <PERSON> on 5/30/24.
//

import SwiftUI
import SwiftData
import Charts

struct YearSummaryView: View {
    @State private var selectedYear: Date = Calendar.current.startOfYear(for: Date())
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var taskListManager: TaskListManager

    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?

    // Data state variables
    @State private var monthlyPracticeTimeData: [(Date, Int)] = []
    @State private var yearPracticeListData: [(String, Int)] = []
    
    // Get the first day of the selected year
    private var yearStartDate: Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year], from: selectedYear)
        return calendar.date(from: components) ?? Date()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            YearCalendarPicker(selectedYearStart: $selectedYear)
            
            List {
                Section {
                    yearMonthlyBreakdownView
                        .listRowSeparator(.hidden)
                        .padding(.vertical)
                }
                
                Section {
                    if yearPracticeListData.isEmpty {
                        EmptyStateView(
                            imageName: "CatPiano",
                            title: String(localized: "No data yet"),
                            subtitle: String(localized: "Looks like there's no practice recorded during this time")
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        PracticeSummaryView(list: yearPracticeListData)
                            .listRowSeparator(.hidden)
                    }
                }
            }
            .listStyle(.insetGrouped)
        }
        .onAppear {
            loadYearData()
        }
        .onChange(of: selectedYear) {
            selectedElement = nil
            loadYearData()
        }
        .background(Color(.secondarySystemBackground))
        .navigationTitle("Year Summary")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private var isCurrentYear: Bool {
        let calendar = Calendar.current
        let currentYearComponents = calendar.dateComponents([.year], from: Date())
        let selectedYearComponents = calendar.dateComponents([.year], from: selectedYear)
        
        return currentYearComponents.year == selectedYearComponents.year
    }
    
    @ViewBuilder
    @MainActor
    private var yearMonthlyBreakdownView: some View {
        VStack(alignment: .leading, spacing: 10) {
            YearMonthlyChartContainerView(
                data: monthlyPracticeTimeData,
                selectedDate: $selectedDate,
                selectedElement: $selectedElement
            )
        }
    }

    private func loadYearData() {
        Task {
            monthlyPracticeTimeData = await taskListManager.getMonthlyPracticeTimeForYear(yearStartDate)
            yearPracticeListData = await taskListManager.getPracticeListForYear(yearStartDate)
        }
    }
}

struct YearMonthlyChartContainerView: View {
    let data: [(Date, Int)]
    @Binding var selectedDate: Date?
    @Binding var selectedElement: (Date, Int)?
    
    var body: some View {
        let maxTimeInHours = max(1, data.map { Double($0.1) / 3600 }.max() ?? 0)

        VStack {
            StatisticsSummaryView(practiceTimeList: data)
                .padding(.bottom)
            
            Chart {
                ForEach(data, id: \.0) { item in
                    let isSelected = if let selectedDate = selectedDate {
                        Calendar.current.isDate(selectedDate, equalTo: item.0, toGranularity: .month)
                    } else {
                        false
                    }
                    
                    BarMark(
                        x: .value("Month", item.0, unit: .month),
                        y: .value("Time", Double(item.1) / 3600)
                    )
                    .foregroundStyle(.gray)
                    .shadow(
                        color: isSelected ? .gray.opacity(0.7) : Color.clear,
                        radius: isSelected ? 5 : 0
                    )
                }
                
                if let selected = selectedElement {
                    let beginRange = data.count > 0 ? data[0].0 : nil
                    let start = Calendar.current.startOfMonth(for: selected.0)
                    let midMonth = Calendar.current.date(byAdding: .day, value: 15, to: start)!
                    
                    // Vertical line at selection
                    RuleMark(x: .value("Selected", midMonth))
                        .foregroundStyle(.gray)
                        .lineStyle(StrokeStyle(lineWidth: 1, dash: [5]))
                    
                    // Point mark at the top of the bar
                    if let beginRange = beginRange {
                        PointMark(
                            x: .value("Month", beginRange),
                            y: .value("Time", maxTimeInHours)
                        )
                        .symbolSize(0)
                        .foregroundStyle(.accent)
                        .annotation(position: .topTrailing) {
                            let (time, unit) = getTimeStringPair(of: selected.1)
                            ChartPointMark(titleNumber: time, titleUnit: unit, subTitle: getMonthAbbr(from: selected.0))
                        }
                    }
                }
            }
            .frame(height: 180)
            .chartXSelection(value: $selectedDate)
            .onChange(of: selectedDate) {
                if let date = selectedDate {
                    selectedElement = data.first(where: { 
                        Calendar.current.isDate($0.0, equalTo: date, toGranularity: .month) 
                    })
                }
            }
            .chartXAxis {
                AxisMarks(values: .stride(by: .month)) { value in
                    if let date = value.as(Date.self) {
                        let month = Calendar.current.component(.month, from: date)
                        AxisValueLabel {
                            Text("\(month)")
                                .roundedBody()
                        }
                    }
                }
            }
            .chartYAxis {
                AxisMarks() { value in
                    AxisTick()
                    AxisValueLabel()
                        .font(.system(size: CustomFontSize.body, weight: .bold, design: .rounded))
                }
            }
            .chartYScale(domain: 0...maxTimeInHours)
        }
    }
}

// Year Calendar Picker component
struct YearCalendarPicker: View {
    @Binding var selectedYearStart: Date
    @State private var showingYearPicker = false
    @State private var selectedYear = Calendar.current.component(.year, from: Date())
    
    var body: some View {
        HStack {
            Button(action: { previousYear() }) {
                Image(systemName: "arrowtriangle.backward.fill")
            }
            .disabled(selectedYear <= 2000)
            
            Spacer()
            

            Text("\(String(selectedYear))")
                    .font(.headline)
            Spacer()
            
            Button(action: { nextYear() }) {
                Image(systemName: "arrowtriangle.forward.fill")
            }
            .disabled(selectedYear >= Calendar.current.component(.year, from: Date()))
        }
        .padding(.horizontal)
        .padding(.vertical)
        .background(Color(.secondarySystemBackground))
        .onAppear {
            selectedYear = Calendar.current.component(.year, from: selectedYearStart)
        }
    }
    
    private func previousYear() {
        selectedYear -= 1
        updateSelectedYear()
    }
    
    private func nextYear() {
        selectedYear += 1
        updateSelectedYear()
    }
    
    private func updateSelectedYear() {
        let calendar = Calendar.current
        if let date = calendar.date(from: DateComponents(year: selectedYear, month: 1, day: 1)) {
            selectedYearStart = date
        }
    }
}

#Preview {
    YearSummaryView()
} 

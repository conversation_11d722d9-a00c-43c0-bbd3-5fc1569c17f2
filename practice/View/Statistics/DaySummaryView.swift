//
//  DaySummaryView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/21.
//

import SwiftUI
import SwiftData
import Charts

struct DaySummaryView: View {
    @State private var selectedDate: Date = Date()
    @StateObject private var taskListManager: TaskListManager = .shared

    // Data state variables
    @State private var dayPracticeListData: [(String, Int)] = []
    
    var body: some View {
        VStack(spacing: 0) {
            DayCalendarPicker(selectedDate: $selectedDate)
            List {
                Section {
                    if dayPracticeListData.isEmpty {
                        EmptyStateView(
                            imageName: "CatPiano",
                            title: String(localized: "No data yet"),
                            subtitle: String(localized: "Looks like there's no practice recorded during this time")
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        PracticeSummaryView(list: dayPracticeListData)
                    }
                }
            }
            .listStyle(.insetGrouped)
        }
        .navigationTitle("Day Summary")
        .background(Color(.secondarySystemBackground))
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadDayData()
        }
        .onChange(of: selectedDate) { _, _ in
            loadDayData()
        }
    }
    
    private var datePickerView: some View {
        HStack {
            Spacer()
            DatePicker(
                "",
                selection: $selectedDate,
                in: ...Date(),
                displayedComponents: .date
            )
            .datePickerStyle(.compact)
            .labelsHidden()

            Spacer()
        }
        .padding(.bottom)
    }

    private func loadDayData() {
        Task {
            dayPracticeListData = await taskListManager.getPracticeListForDate(selectedDate)
        }
    }
}

#Preview {
    DaySummaryView()
}

//
//  MonthSummaryView.swift
//  practice
//
//  Created by <PERSON> on 5/30/24.
//

import SwiftUI
import SwiftData
import Charts

struct MonthSummaryView: View {
    @State private var selectedMonth: Date = Calendar.current.startOfMonth(for: Date())
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var taskListManager: TaskListManager

    @State private var selectedDate: Date?
    @State private var selectedElement: (Date, Int)?

    // Data state variables
    @State private var dailyPracticeTimeData: [(Date, Int)] = []
    @State private var monthPracticeListData: [(String, Int)] = []
    
    // Get the first day of the selected month
    private var monthStartDate: Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: selectedMonth)
        return calendar.date(from: components) ?? Date()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            MonthCalendarPicker(selectedMonthStart: $selectedMonth)
            
            List {
                Section {
                    monthDailyBreakdownView
                        .listRowSeparator(.hidden)
                        .padding(.vertical)
                }
                
                Section {
                    if monthPracticeListData.isEmpty {
                        EmptyStateView(
                            imageName: "CatPiano",
                            title: String(localized: "No data yet"),
                            subtitle: String(localized: "Looks like there's no practice recorded during this time")
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        PracticeSummaryView(list: monthPracticeListData)
                            .listRowSeparator(.hidden)
                    }
                }
            }
            .listStyle(.insetGrouped)
        }
        .background(Color(.secondarySystemBackground))
        .navigationTitle("Month Summary")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadMonthData()
        }
        .onChange(of: selectedMonth) { oldValue, newValue in
            // 清除选中状态
            selectedDate = nil
            selectedElement = nil
            loadMonthData()
        }
    }
    
    private var isCurrentMonth: Bool {
        let calendar = Calendar.current
        let currentMonthComponents = calendar.dateComponents([.year, .month], from: Date())
        let selectedMonthComponents = calendar.dateComponents([.year, .month], from: selectedMonth)
        
        return currentMonthComponents.year == selectedMonthComponents.year &&
               currentMonthComponents.month == selectedMonthComponents.month
    }
    
    @ViewBuilder
    @MainActor
    private var monthDailyBreakdownView: some View {
        VStack(alignment: .leading, spacing: 10) {
            MonthDailyChartContainerView(
                data: dailyPracticeTimeData,
                selectedDate: $selectedDate,
                selectedElement: $selectedElement
            )
        }
    }

    private func loadMonthData() {
        Task {
            dailyPracticeTimeData = await taskListManager.getDailyPracticeTimeForMonth(monthStartDate)
            monthPracticeListData = await taskListManager.getPracticeListForMonth(monthStartDate)
        }
    }
}

struct MonthDailyChartContainerView: View {
    let data: [(Date, Int)]
    @Binding var selectedDate: Date?
    @Binding var selectedElement: (Date, Int)?
    
    var body: some View {
        VStack {
            StatisticsSummaryView(practiceTimeList: data)
                .padding(.bottom)
            
            ChartView(
                data: data,
                unit: TaskStatisticsNavigator.StatisticsUnit.month,
                selectedDate: selectedDate,
                selectedElement: selectedElement,
                showTarget: true
            )
            .frame(height: 180)
            .chartXSelection(value: $selectedDate)
            .onChange(of: selectedDate) {
                if let date = selectedDate {
                    selectedElement = data.first(where: { Calendar.current.isDate($0.0, inSameDayAs: date) })
                }
            }
        }
    }
}

// Month Calendar Picker component
struct MonthCalendarPicker: View {
    @Binding var selectedMonthStart: Date
    @State private var selectedYear = Calendar.current.component(.year, from: Date())
    @State private var selectedMonth = Calendar.current.component(.month, from: Date())
    
    var body: some View {
        HStack {
            Button(action: { previousMonth() }) {
                Image(systemName: "arrowtriangle.backward.fill")
            }
            .disabled(selectedYear == 2000 && selectedMonth == 1) // Assuming 2000-01 is the earliest allowed
            
            Spacer()
            
            Text("\(getMonthName(from: selectedMonthStart)) \(String(selectedYear))")
                .font(.headline)
            
            Spacer()
            
            Button(action: { nextMonth() }) {
                Image(systemName: "arrowtriangle.forward.fill")
            }
            .disabled(isCurrentMonthOrFuture())
        }
        .padding(.horizontal)
        .padding(.vertical)
        .background(Color(.secondarySystemBackground))
        .onAppear {
            let calendar = Calendar.current
            selectedYear = calendar.component(.year, from: selectedMonthStart)
            selectedMonth = calendar.component(.month, from: selectedMonthStart)
        }
    }
    
    private func previousMonth() {
        if selectedMonth > 1 {
            selectedMonth -= 1
        } else {
            selectedMonth = 12
            selectedYear -= 1
        }
        updateSelectedMonth()
    }
    
    private func nextMonth() {
        if selectedMonth < 12 {
            selectedMonth += 1
        } else {
            selectedMonth = 1
            selectedYear += 1
        }
        updateSelectedMonth()
    }
    
    private func updateSelectedMonth() {
        let calendar = Calendar.current
        if let date = calendar.date(from: DateComponents(year: selectedYear, month: selectedMonth, day: 1)) {
            selectedMonthStart = date
        }
    }
    
    private func isCurrentMonthOrFuture() -> Bool {
        let calendar = Calendar.current
        let currentYear = calendar.component(.year, from: Date())
        let currentMonth = calendar.component(.month, from: Date())
        
        return (selectedYear > currentYear) || 
               (selectedYear == currentYear && selectedMonth >= currentMonth)
    }
    
    private func getMonthName(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM"
        return formatter.string(from: date)
    }
}

#Preview {
    MonthSummaryView()
} 

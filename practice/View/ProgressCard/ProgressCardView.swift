//
//  ProgressCardView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/1.
//

import SwiftUI

struct ProgressCardView: View {
    var date: Date = Date()
    var taskProgressDictionary: [Date: TaskStatus] = [:]
    private let rows: Int = 3 // Fixed number of rows
    private let calendar = Calendar.current
    private var numberOfDaysInMonth: Int {
        calendar.range(of: .day, in: .month, for: date)?.count ?? 0
    }
    private var monthName: String {
        let dateFormatter = DateFormatter()
        dateFormatter.locale = Locale(identifier: "en_US")
        dateFormatter.dateFormat = "MMM"
        let monthAbbr = dateFormatter.string(from: date)
        return monthAbbr
    }
    private var yearName: Int {
        let components = calendar.dateComponents([.year, .month], from: date)
        return components.year!
    }
    
    private var columns: [GridItem] {
        let totalColumns = Int(ceil(Double(numberOfDaysInMonth) / Double(rows)))
        return Array(repeating: .init(.flexible()), count: totalColumns)
    }
    
    private var datesInMonth: [Date] {
        (1...numberOfDaysInMonth).compactMap { day -> Date? in
            var components = calendar.dateComponents([.year, .month], from: date)
            components.day = day
            return calendar.date(from: components)
        }
    }
    
    init(date: Date, taskProgress: [TaskProgress]) {
        self.date = date
        // Convert array to dictionary for quick lookup
        self.taskProgressDictionary = taskProgress.reduce(into: [:]) { result, progress in
            let key = calendar.startOfDay(for: progress.date)
            result[key] = progress.status
        }
    }
    
    private func color(for date: Date) -> Color {
        // Improved lookup time
        let status = taskProgressDictionary[calendar.startOfDay(for: date)]
        switch status {
        case .notStart:
            return .gray.opacity(0.3)
        case .finished:
            return .green.opacity(0.7)
        default:
            return .gray.opacity(0.3)
        }
    }
    
    var body: some View {
        // Calendar day dots
        let calendar = Calendar.current
                // Creates dates for each day of the current month
                let datesInMonth = (1...numberOfDaysInMonth).compactMap { day -> Date? in
                    var components = calendar.dateComponents([.year, .month], from: self.date)
                    components.day = day
                    return calendar.date(from: components)
                }
        
        VStack {
            HStack {
                Text(String(yearName))
                    .font(.headline)
                    .foregroundColor(.gray)
                Text("\(monthName)")
                    .font(.headline)
                    .foregroundColor(.gray)
                Spacer()
            }.padding(.top)
            HStack {
                LazyVGrid(columns: columns, alignment: .center, spacing: 5) {
                    ForEach(datesInMonth, id: \.self) { date in
                        Rectangle()
                            .frame(width: 20, height: 20)
                            .foregroundColor(color(for: date))
                            .cornerRadius(4)
                    }
                }.frame(width: CGFloat(ceil(Double(numberOfDaysInMonth) / Double(rows))) * 25 + 10).padding(2)
                Spacer()
            }
        }
    }
}

#Preview {
    ProgressCardView(date: Date(), taskProgress: [])
}

//
//  MonthCalendarProgressCardView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/27.
//

import SwiftUI

struct MonthCalendarProgressCardView: View {
    var date: Date = Date()
    var taskProgressDictionary: [Date: TaskProgress] = [:]

    private let rows: Int = 5
    private let calendar = Calendar.current
    private var numberOfDaysInMonth: Int {
        calendar.range(of: .day, in: .month, for: date)?.count ?? 0
    }
    private var yearName: Int {
        let components = calendar.dateComponents([.year, .month], from: date)
        return components.year!
    }
    
    private var columns: [GridItem] {
        let totalColumns = 7
        return Array(repeating: .init(.flexible()), count: totalColumns)
    }
    
    private var datesInMonth: [Date] {
        (1...numberOfDaysInMonth).compactMap { day -> Date? in
            var components = calendar.dateComponents([.year, .month], from: date)
            components.day = day
            return calendar.date(from: components)
        }
    }
    
    init(date: Date, taskProgress: [TaskProgress]) {
        self.date = date
        self.taskProgressDictionary = taskProgress.reduce(into: [:]) { result, progress in
            let key = calendar.startOfDay(for: progress.date)
            result[key] = progress
        }
    }
    
    private func color(for date: Date) -> Color {
        let practiceTime = taskProgressDictionary[calendar.startOfDay(for: date)]?.practiceTime ?? 0
        return calcCardCellColorFrom(seconds: practiceTime)
    }
    
    var body: some View {
        let calendar = Calendar.current
        let datesInMonth = (1...numberOfDaysInMonth).compactMap { day -> Date? in
            var components = calendar.dateComponents([.year, .month], from: self.date)
            components.day = day
            return calendar.date(from: components)
        }
        
        VStack {
            HStack {
                Text(String(yearName))
                Text("\(getMonthAbbr(from: date))")
                Spacer()
            }
            .font(.headline)
            .foregroundColor(.gray)
            .padding(.top)
            HStack {
                LazyVGrid(columns: columns, alignment: .center, spacing: 5) {
                    ForEach(datesInMonth, id: \.self) { date in
                        Rectangle()
                            .frame(width: 8, height: 8)
                            .foregroundColor(color(for: date))
                            .cornerRadius(2)
                    }
                }.padding(2)
                Spacer()
            }
        }
        .containerRelativeFrame(.horizontal)
    }
}

#Preview {
    MonthCalendarProgressCardView(date: Date(), taskProgress: [])
}

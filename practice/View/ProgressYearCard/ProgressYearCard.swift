//
//  ProgressYearCard.swift
//  practice
//
//  Created by Ulyssis on 2024/2/19.
//

import SwiftUI
import SwiftData

struct ProgressYearCard: View {
    @Query var personalConfigs: [PersonalConfig]
    var personalConfig: PersonalConfig? {
        personalConfigs.first
    }
    
    var date: Date = Date()
    var cellWidth = CGFloat(ceil(Double((UIScreen.main.bounds.width * 0.8) / 31) * 0.65))
    private let rows: Int = 12 // Fixed number of rows
    private let showName = false // show "Practice"
    private let calendar = Calendar.current
    private var numberOfDaysInYear: Int {
        calendar.range(of: .day, in: .year, for: date)?.count ?? 0
    }
    private var yearName: Int {
        let components = calendar.dateComponents([.year, .month], from: date)
        return components.year!
    }
    
    private var columns: [GridItem] {
        let totalColumns = Int(ceil(Double(numberOfDaysInYear) / Double(rows)))
        return Array(repeating: .init(.flexible()), count: totalColumns)
    }

    private var datesInYear: [Date] {
        (1...numberOfDaysInYear).compactMap { day -> Date? in
            var components = calendar.dateComponents([.year, .month], from: date)
            components.day = day
            return calendar.date(from: components)
        }
    }
    
    // group by months
    private var datesInYearArr: [[Date]] {
        var monthsArray = [[Date]]()
        // Start by finding the first day of the year
        guard let _ = calendar.date(from: DateComponents(year: yearName, month: 1, day: 1)) else { return [] }
        // Iterate through each month
        for month in 1...12 {
            var monthDays = [Date]()
            guard let rangeOfDays = calendar.range(of: .day, in: .month, for: calendar.date(from: DateComponents(year: yearName, month: month))!) else {
                continue
            }
            // Iterate through each day of the month
            for day in rangeOfDays {
                if let date = calendar.date(from: DateComponents(year: yearName, month: month, day: day)) {
                    monthDays.append(date)
                }
            }
            monthsArray.append(monthDays)
        }
        
        return monthsArray
    }
    
    init(date: Date) {
        self.date = date
    }
    
    @MainActor
    private func color(for date: Date) -> Color {
        let practiceTime = TaskProgressData.shared.getPracticeList(of: date, by: .day).reduce(0) { sum, practice in
            sum + practice.1
        }
        return calcCardCellColorFrom(seconds: practiceTime, targetTime: personalConfig?.dailyPracticeGoal ?? nil)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                Text(String(yearName))
                    .font(.footnote)
                    .fontWeight(.bold)
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                
                let yearPractices = TaskProgressData.shared.getPracticeList(of: date, by: .year)
                let totalSeconds = yearPractices.reduce(0) { sum, practice in
                    sum + practice.1
                }
                
                let str = totalSeconds < 3600 
                    ? String(localized: "\(totalSeconds / 60) minute") 
                    : String(localized: "\(totalSeconds / 3600) hour")
                
                Text("\(str)")
                    .font(.caption)
                    .bold()
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                    .padding(.leading, 20)
                
                // Calculate practice days count - we can estimate from the year practices
                let practiceDays = yearPractices.isEmpty ? 0 : yearPractices.count
                
                Text("\(practiceDays) days")
                    .font(.caption)
                    .bold()
                    .foregroundColor(.gray)
                    .padding(.bottom, 0)
                    .padding(.leading, 20)
                Spacer()
                Text(verbatim: "Practice")
                    .font(.footnote)
                    .fontWeight(.bold)
                    .foregroundColor(showName ? .gray : .clear)
                    .padding(.bottom, 0)
            }
            Spacer()
            HStack(spacing: 0) {
                VStack(spacing: cellWidth * 0.5) {
                    ForEach(datesInYearArr, id: \.self) { dateArr in
                        VStack(spacing: cellWidth * 0.5) {
                            HStack(spacing: cellWidth * 0.5) {
                                ForEach(dateArr, id: \.self) { date in
                                    Rectangle()
                                        .frame(width: cellWidth, height: cellWidth)
                                        .foregroundColor(color(for: date))
                                        .cornerRadius(2)
                                }
                                Spacer()
                            }.padding(0)
                        }.padding(0)
                    }
                }.padding(.vertical, 0)
                
                VStack {
                    ForEach(0..<6, id: \.self) { month in
                        Text("\(getMonthAbbrList()[month * 2])")
                            .font(.system(size: cellWidth * 1.3))
                            .foregroundColor(.gray)
                            .fontWeight(.bold)
                            .padding(0)
                        Spacer()
                    }
                }
            }
        }
    }
}

#Preview {
    ProgressYearCard(date: Date())
}

//
//  LoadingView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/23.
//

import SwiftUI

struct LoadingView: View {
    var body: some View {
        VStack {
            HStack(spacing: 20) {
                Image("Logo")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 100, height: 100)
                    .cornerRadius(24)
                    .shadow(radius: 10)
                VStack(alignment: .leading) {
                    Text("Only Practice")
                        .font(.title)
                        .fontWeight(.bold)
                    Text("Can Make Perfect")
                        .font(.title3)
                        .fontWeight(.bold)
                }
            }
            ProgressView()
        }
    }
}

#Preview {
    LoadingView()
}

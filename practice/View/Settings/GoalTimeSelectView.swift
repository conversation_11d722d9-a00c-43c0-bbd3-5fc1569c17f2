//
//  GoalTimeSelectView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/15.
//

import SwiftUI
import <PERSON>tie

struct GoalTimeSelectView: View {
    @Binding var goalMinute: Int
    var body: some View {
        VStack(spacing: 0) {
            LottieView(animation: .named("pianist"))
                .playbackMode(.playing(.toProgress(1, loopMode: .autoReverse)))
                .scaledToFit()
                .ignoresSafeArea()
            Text("Set Your Daily Practice Goal")
                .font(.custom("Nunito-Bold", size: 24))
                .padding(.horizontal)
            Text("Don’t stress — just choose a goal you can stick to!")
                .font(.custom("Nunito-Bold", size: 20))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
                .padding(.horizontal)
            GoalTimeSelector(goalMinute: $goalMinute)
        }
    }
}

struct GoalTimeSelectViewForSetting: View {
    @Binding var goalMinute: Int
    var body: some View {
        GoalTimeSelector(goalMinute: $goalMinute)
    }
}

struct GoalTimeSelector: View {
    private let goalMinuteList = stride(from: 5, through: 1440, by: 5).map { $0 }
    @Binding var goalMinute: Int
    
    var body: some View {
        HStack {
            Picker(selection: $goalMinute, content: {
                ForEach(goalMinuteList, id: \.self) { minute in
                    Text("\(minute)")
                }
            }, label: {
                Text("Daily Practicing Goal")
            })
            .frame(width: 150)
            .pickerStyle(.wheel)
            Text("min/day")
                .padding(.horizontal)
        }
    }
}

#Preview {
    @Previewable @State var minute = 0
    GoalTimeSelectViewForSetting(goalMinute: $minute)
}

//
//  OnboardingView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI
import Lottie


fileprivate let titleFontSize = 20.0
fileprivate let descFontSize = 16.0
fileprivate let animationBase = 0.1

struct OnboardingView: View {
    @State private var showSetup = true
    @State private var currentStep = 0
    @Environment(\.dismiss) private var dismiss
    private let totalSteps = 4
    
    static var hasSeenOnboarding: Bool {
        get {
            UserDefaults.standard.bool(forKey: "hasSeenOnboarding")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "hasSeenOnboarding")
        }
    }
    
    var body: some View {
        if showSetup {
            StepGuider<AnyView>(
                currentStep: $currentStep,
                totalSteps: totalSteps,
                illustration: AnyView(
                    LottieView(animation: .named("pianist"))
                        .playbackMode(.playing(.toProgress(1, loopMode: .autoReverse)))
                        .scaledToFit()
                ),
                stepContents: [
                    0: { AnyView(GoalStepView(onNext: { currentStep += 1 })) },
                    1: { AnyView(TaskCreateView(onNext: { currentStep += 1 })) },
                    2: { AnyView(ReminderStepView(onNext: { currentStep += 1 })) },
                    3: { AnyView(TrialStepView(onNext: {  })) }
                ],
                onComplete: {
                    showSetup = false
                    // Navigate to main app or close onboarding
                    dismiss()
                },
                onSkip: {
                    if currentStep < totalSteps - 1 {
                        currentStep += 1
                    } else {
                        dismiss()
                    }
                }
            )
            .onAppear {
                OnboardingView.hasSeenOnboarding = true
            }
        }
    }
}

struct ReminderStepView: View {
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var appear = false
    
    var onNext: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            GuideTitleDescription(title: String(localized: "Schedule Your Reminders"), desc: String(localized: "Choose the best time for your daily practice reminder."))
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 2), value: appear)
            ReminderTimeSelector(reminderTime: $notificationManager.reminderTime)
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 3), value: appear)
            Spacer()
            GuideButton(text: String(localized: "Next"), action: {
                notificationManager.requestNotificationPermission { granted in
                    if granted {
                        notificationManager.dailyReminderEnabled = true
                        notificationManager.scheduleDailyReminder()
                    } else {
                        notificationManager.dailyReminderEnabled = false
                    }
                    onNext()
                }
            })
            .offset(y: appear ? 0 : 50)
            .opacity(appear ? 1 : 0)
            .animation(.easeOut(duration: animationBase * 4), value: appear)
        }
        .onAppear {
            appear = true
        }
    }
}

struct TrialStepView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var appear = false
    @State private var showMemberShipSubscription = false
    @StateObject private var membershipManager: MembershipManager = .shared
    @StateObject private var notificationManager = NotificationManager.shared
    
    var onNext: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            if !appear {
                ProgressView()
            }
            if let product = membershipManager.getProduct(productID: membershipManager.yearlyProductID) {
                ScrollView(.vertical) {
                    GuideTitleDescription(title: String(localized: "Start Free Trial"), desc: String(localized: "Don't worry — we'll remind you before your trial ends!"))
                        .offset(y: appear ? 0 : 50)
                        .opacity(appear ? 1 : 0)
                        .animation(.easeOut(duration: animationBase), value: appear)
                    HStack(spacing: 10) {
                        VStack(alignment: .leading, spacing: 20) {
                            CellItem(
                                systemImageName: "lock.fill",
                                title: String(localized: "Today"),
                                desc: String(localized: "Start using all features of Only Practice now to level up every practice.")
                            )
                                .padding(.top)
                                .offset(y: appear ? 0 : 50)
                                .opacity(appear ? 1 : 0)
                                .animation(.easeOut(duration: animationBase * 3), value: appear)
                            
                            CellItem(
                                systemImageName: "bell.fill",
                                title: String(localized: "Day 5"),
                                desc: String(localized: "Enable notifications so Only Practice can remind you two days before your trial ends.")
                            )
                                .offset(y: appear ? 0 : 50)
                                .opacity(appear ? 1 : 0)
                                .animation(.easeOut(duration: animationBase * 4), value: appear)
                            
                            CellItem(
                                systemImageName: "star.fill",
                                title: String(localized: "Day 7"),
                                desc: String(localized: "No charge until Day 7. Cancel anytime before to avoid payment.")
                            )
                                .padding(.bottom)
                                .offset(y: appear ? 0 : 50)
                                .opacity(appear ? 1 : 0)
                                .animation(.easeOut(duration: animationBase * 5), value: appear)
                        }
                        .background(
                            ZStack {
                                Rectangle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.accentColor, Color.accentColor.opacity(0.0)]),
                                            startPoint: .top,
                                            endPoint: .bottom
                                        )
                                    )
                                    .frame(width: 30)
                                    .padding(.top, 30)
                                Rectangle()
                                    .fill(Color.accentColor)
                                    .frame(width: 30)
                                    .cornerRadius(15)
                                    .padding(.top, 10)
                                    .padding(.bottom, 30)
                            }
                                .offset(y: appear ? 0 : 50)
                                .opacity(appear ? 1 : 0)
                                .animation(.easeOut(duration: animationBase), value: appear),
                            alignment: .leading
                        )
                    }
                    
                    Spacer()
                }
                
                Text("Then, \(product.displayPrice)/year")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .offset(y: appear ? 0 : 50)
                    .opacity(appear ? 1 : 0)
                    .animation(.easeOut(duration: animationBase * 6), value: appear)
                
                GuideButton(text: String(localized: "Start 7-Day Free Trial"), action: {
                    Task {
                        do {
                            let success = try await membershipManager.purchase(product: product)
                            if success {
                                // 设置 5 天后的提醒
                                if let reminderDate = Calendar.current.date(byAdding: .day, value: 5, to: Date()) {
                                    notificationManager.requestNotificationPermission(completion: { _ in
                                        notificationManager.scheduleTrialEndingReminder(on: reminderDate)
                                    })
                                }
                                
                                dismiss()
                                
                                await membershipManager.updateMembershipStatus()
                                
                            }
                        } catch {
                            print("Purchase failed: \(error)")
                        }
                    }
                })
                .padding(.vertical)
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 7), value: appear)
                
                Button(action: {
                    showMemberShipSubscription = true
                }) {
                    Text("See All Membership Options")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .offset(y: appear ? 0 : 50)
                        .opacity(appear ? 1 : 0)
                        .animation(.easeOut(duration: animationBase * 8), value: appear)
                }
            }
        }
        .onChange(of: membershipManager.status) {
            if membershipManager.status != .free {
                dismiss()
            }
        }
        .onChange(of: membershipManager.products) {
            if membershipManager.products.count > 0 {
                appear = true
            }
        }
        .onAppear {
//            appear = true
//            TODO: 判断是否有 7 天免费试用权限
            Task {
                if let product = membershipManager.getProduct(productID: membershipManager.yearlyProductID), let _ = await membershipManager.freeTrialDescription(for: product) {
                    await MainActor.run {
                        appear = true
                    }
                }
            }
        }
        .sheet(isPresented: $showMemberShipSubscription) {
            CustomSubscribeView()
        }
    }
    
    struct CellItem: View {
        let systemImageName: String
        let title: String
        let desc: String
        
        var body: some View {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 0) {
                    Image(systemName: systemImageName)
                        .foregroundColor(.white)
                        .frame(width: 30)
                        .padding(.trailing, 10)
                    Text(title)
                        .font(.custom("Nunito-Bold", size: titleFontSize))
                }
                Text(desc)
                    .padding(.leading, 40)
                    .padding(.trailing)
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
                    .foregroundColor(.gray)
                    .font(.custom("Nunito", size: 14))
            }
        }
    }
}

fileprivate struct GuideTitleDescription: View {
    let title: String
    let desc: String
    
    var body: some View {
        VStack(spacing: 0) {
            Text(title)
                .font(.custom("Nunito-Bold", size: 20))
                .font(.largeTitle)
            Text(desc)
                .font(.custom("Nunito-Bold", size: 16))
                .fixedSize(horizontal: false, vertical: true)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

fileprivate struct GuideButton: View {
    let text: String
    let action: () -> Void
    var body: some View {
        Button(action: {
            action()
        }) {
            Text(text)
                .font(.custom("Nunito-Bold", size: titleFontSize))
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.accentColor)
                .foregroundColor(.white)
                .cornerRadius(28)
                .padding(.horizontal)
        }
    }
}

fileprivate struct GoalStepView: View {
    @State private var goalMinute: Int = 30
    @State private var appear = false
    @StateObject private var configManager: ConfigManager = .shared

    var onNext: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            GuideTitleDescription(title: String(localized: "Set Your Daily Practice Goal"), desc: String(localized:"Don’t stress — just choose a goal you can stick to!"))
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase), value: appear)
            GoalTimeSelector(goalMinute: $goalMinute)
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 2), value: appear)
                .onChange(of: goalMinute, initial: true) {
                    configManager.updateDailyGoal(goalMinute)
                }
            Spacer()
            GuideButton(text: String(localized:"Next"), action: { onNext() })
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 3), value: appear)
        }
        .onAppear {
            appear = true
        }
    }
}

fileprivate struct TaskCreateView: View {
    var onNext: () -> Void
    @State private var appear = false
    @State private var taskTitle: String = ""
    @StateObject private var taskListManager: TaskListManager = .shared
    
    var body: some View {
        VStack {
            GuideTitleDescription(title: String(localized: "Create Your First Practice Task"), desc: String(localized: "What piece are you practicing? Enter a title, e.g. Mozart Sonata"))
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase), value: appear)
            Spacer()
            TextField(String(localized: "Enter a title, e.g. Mozart Sonata"), text: $taskTitle)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 2), value: appear)
                .padding(.horizontal)
            Spacer()
            GuideButton(text: String(localized:"Next"), action: {
                let task = TaskItem(
                    pieceName: taskTitle,
                    composerName: "",
                    key: .cMajor,
                    difficulty: .one,
                    beginDate: Date()
                )
                taskListManager.createNewTask(task)
                onNext()
            })
                .offset(y: appear ? 0 : 50)
                .opacity(appear ? 1 : 0)
                .animation(.easeOut(duration: animationBase * 3), value: appear)
                .disabled(taskTitle.isEmpty)
        }
        .onAppear {
            appear = true
        }
    }
}

struct OnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
    }
}

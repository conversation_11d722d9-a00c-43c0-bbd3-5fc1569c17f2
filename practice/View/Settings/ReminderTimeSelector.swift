//
//  ReminderTimeSelector.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI

struct ReminderTimeSelector: View {
    @Binding var reminderTime: Date
    var body: some View {
        VStack {
            DatePicker("", selection: $reminderTime, displayedComponents: .hourAndMinute)
                .datePickerStyle(WheelDatePickerStyle())
                .labelsHidden()
                .frame(maxWidth: .infinity)
                .padding()
            .padding(.bottom)
        }
    }
}

#Preview {
    @Previewable @State var reminderTime = Date()
    ReminderTimeSelector(reminderTime: $reminderTime)
}

//
//  ViewExtension.swift
//  practice
//
//  Created by <PERSON> on 2025/5/21.
//

import SwiftUI

extension View {
    func roundedLargeTitle() -> some View {
        self.font(.system(size: CustomFontSize.largeTitle, weight: .bold, design: .rounded))
    }
    
    func roundedTitle() -> some View {
        self.font(.system(size: CustomFontSize.title, weight: .bold, design: .rounded))
    }
    
    func roundedBody() -> some View {
        self.font(.system(size: CustomFontSize.body, weight: .bold, design: .rounded))
    }
}

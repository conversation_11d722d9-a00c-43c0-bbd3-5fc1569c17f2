//
//  ColorThemeManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/25.
//

import Foundation
import SwiftUI

@MainActor
class ColorThemeManager: ObservableObject {
    static var shared = ColorThemeManager()
    private let key = "colorScheme"
    
    
      let themeOptions = [
          String(localized: "system"),
          String(localized: "light"),
          String(localized: "dark")
      ]
    
    var selectedTheme: String {
        get { storedColorScheme }
        set { storedColorScheme = newValue }
    }
    
    @AppStorage("colorScheme") private var storedColorScheme: String = String(localized: "system") {
        didSet {
            objectWillChange.send()
        }
    }
    
    var preferredColorScheme: ColorScheme? {
        switch storedColorScheme {
        case String(localized: "light"): return .light
        case String(localized: "dark"): return .dark
        default: return nil
        }
    }
}

//
//  CollectionManager.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import SwiftUI
import SwiftData
import Combine

@MainActor
class CollectionManager: ObservableObject {
    let modelContext: ModelContext = TaskItemSchemaV1.container.mainContext
    private lazy var imageManager = ImageManager(modelContext: modelContext)
    static let shared = CollectionManager()

    @Published var collections: [CollectionItem] = []
    @Published var allAchievements: [TaskItem] = []

    // Track whether user has manually deleted the default collection
    @Published private var hasUserDeletedDefaultCollection: Bool = false

    init() {
//        TODO
//        loadUserPreferences()
        loadData()
    }
    
    // MARK: - Data Loading
    func loadData() {
        do {
            let taskDescriptor = FetchDescriptor<TaskItem>(
                sortBy: [SortDescriptor(\.lastModified, order: .reverse)]
            )

            allAchievements = try modelContext.fetch(taskDescriptor).filter { task in task.taskType == .Achievement }

            // Load all collections and handle default collection auto-creation
            loadCollections()

            objectWillChange.send()
        } catch {
            print("Error fetching collection data: \(error)")
            collections = []
            allAchievements = []
        }
    }

    private func loadCollections() {
        do {
            let collectionDescriptor = FetchDescriptor<CollectionItem>(
                sortBy: [SortDescriptor(\.order, order: .forward)]
            )
            var allCollections = try modelContext.fetch(collectionDescriptor)

            // Handle default collection logic
            let defaultCollections = allCollections.filter { $0.type == .defaultCollection }

            // Clean up duplicate default collections if any exist
            if defaultCollections.count > 1 {
                cleanupDuplicateDefaultCollections(defaultCollections)
                // Reload after cleanup
                allCollections = try modelContext.fetch(collectionDescriptor)
            }

            // Check if we need to create default collection
            let hasDefaultCollection = allCollections.contains { $0.type == .defaultCollection }

            if !hasDefaultCollection && !hasUserDeletedDefaultCollection && !allAchievements.isEmpty {
                // Create default collection
                let defaultCollection = createDefaultCollection()
                allCollections.append(defaultCollection)
            }

            // Update task items for default collection if it exists
            if let defaultCollection = allCollections.first(where: { $0.type == .defaultCollection }) {
                defaultCollection.taskItems = allAchievements
            }

            // Sort collections: default collection first (order -1), then user collections
            collections = allCollections.sorted { lhs, rhs in
                return lhs.order < rhs.order
            }

        } catch {
            print("Error fetching collections: \(error)")
            collections = []
        }
    }
    
    // MARK: - Default Collection Logic
    func getDefaultCollection() -> CollectionItem? {
        return collections.first { $0.type == .defaultCollection }
    }

    private func createDefaultCollection() -> CollectionItem {
        let defaultCollection = CollectionItem(
            name: "All Achievement",
            cover: "",
            desc: "All achievements",
            type: .defaultCollection,
            order: -1
        )
        defaultCollection.taskItems = allAchievements

        // Save to persistent store
        modelContext.insert(defaultCollection)
        try? modelContext.save()

        return defaultCollection
    }

    private func cleanupDuplicateDefaultCollections(_ defaultCollections: [CollectionItem]) {
        // Keep the latest one (first in the sorted array), delete the rest
        let collectionsToDelete = Array(defaultCollections.dropFirst())

        for collection in collectionsToDelete {
            print("Removing duplicate default collection: \(collection.id)")

            // 删除关联的封面图片文件
            if !collection.cover.isEmpty {
                imageManager.deleteImage(path: collection.cover)
            }

            modelContext.delete(collection)
        }

        try? modelContext.save()
    }
    
    // MARK: - Collection CRUD Operations
    func createCollection(name: String, desc: String = "", cover: String = "", selectedTasks: [TaskItem] = []) {
        // Get max order from user-created collections only (default collection has order -1)
        let userCollections = collections.filter { $0.type == .userCreated }
        let newOrder = (userCollections.map { $0.order }.max() ?? 0) + 1

        let collection = CollectionItem(
            name: name,
            cover: cover,
            desc: desc,
            type: .userCreated,
            order: newOrder
        )

        // Add selected tasks to the collection
        collection.taskItems = selectedTasks

        modelContext.insert(collection)
        try? modelContext.save()
        loadData()

        // 设置新创建的集合为聚焦项
        AppGlobalStateManager.shared.setCollectionToFocus(collection.id)
    }
    
    func updateCollection(_ collection: CollectionItem, name: String? = nil, desc: String? = nil, cover: String? = nil, tasks: [TaskItem]? = nil) {
        if let name = name {
            collection.name = name
        }
        if let desc = desc {
            collection.desc = desc
        }
        if let cover = cover {
            collection.cover = cover
        }
        if let tasks = tasks {
            collection.taskItems = tasks
        }
        collection.lastModified = Date()
        
        try? modelContext.save()
        loadData()
    }
    
    func deleteCollection(_ collection: CollectionItem) {
        // If deleting a default collection, mark it as user-deleted
        if collection.type == .defaultCollection {
            hasUserDeletedDefaultCollection = true
            saveUserPreferences()
        }

        // 删除关联的封面图片文件
        if !collection.cover.isEmpty {
            imageManager.deleteImage(path: collection.cover)
        }

        modelContext.delete(collection)
        try? modelContext.save()
        loadData()
    }
    
    // MARK: - Collection Ordering
    func reorderCollections(from source: IndexSet, to destination: Int) {
        // Only allow reordering of user-created collections
        // Default collection should always stay at the top
        var userCollections = collections.filter { $0.type == .userCreated }
        userCollections.move(fromOffsets: source, toOffset: destination)

        // Update order values for user collections only
        for (index, collection) in userCollections.enumerated() {
            collection.order = index
        }

        try? modelContext.save()
        loadData()
    }

    func applyCustomOrder(_ orderedCollections: [CollectionItem]) {
        // Apply the new order to collections
        for (index, collection) in orderedCollections.enumerated() {
            collection.order = index
        }

        try? modelContext.save()
        loadData()
    }
    
    // MARK: - Task Management within Collections
    func addTaskToCollection(_ task: TaskItem, collection: CollectionItem) {
        collection.addTask(task)
        try? modelContext.save()
        loadData()
    }
    
    func removeTaskFromCollection(_ task: TaskItem, collection: CollectionItem) {
        collection.removeTask(task)
        try? modelContext.save()
        loadData()
    }
    
    func reorderTasksInCollection(_ collection: CollectionItem, from source: IndexSet, to destination: Int) {
        collection.reorderTasks(from: source, to: destination)
        try? modelContext.save()
        loadData()
    }
    
    // MARK: - Helper Methods
    func getCollectionById(_ id: UUID) -> CollectionItem? {
        return collections.first { $0.id == id }
    }
    
    func getCollectionsContaining(_ task: TaskItem) -> [CollectionItem] {
        return collections.filter { collection in
            collection.taskItems?.contains { $0.id == task.id } ?? false
        }
    }
    
    func getAvailableTasksForCollection(_ collection: CollectionItem) -> [TaskItem] {
        let collectionTaskIds = Set(collection.taskItems?.map { $0.id } ?? [])
        return allAchievements.filter { !collectionTaskIds.contains($0.id) }
    }
    
    // MARK: - Image Management
    func updateCollectionCover(_ collection: CollectionItem, imagePath: String) {
        collection.cover = imagePath
        collection.lastModified = Date()
        try? modelContext.save()
        loadData()
    }

    // MARK: - User Preferences Management
    private func loadUserPreferences() {
        hasUserDeletedDefaultCollection = UserDefaults.standard.bool(forKey: "hasUserDeletedDefaultCollection")
    }

    private func saveUserPreferences() {
        UserDefaults.standard.set(hasUserDeletedDefaultCollection, forKey: "hasUserDeletedDefaultCollection")
    }

    // MARK: - Reset Default Collection (for testing or user preference reset)
    func resetDefaultCollectionPreference() {
        hasUserDeletedDefaultCollection = false
        saveUserPreferences()
    }
}

//
//  AppGlobalStateManager.swift
//  practice
//
//  Created by <PERSON> on 2025/5/29.
//

import Foundation
import SwiftData

// Import TaskListType enum
enum TaskListType: String, CaseIterable {
    case ongoing = "Ongoing"
    case finished = "Finished"

    var displayName: String {
        switch self {
        case .ongoing:
            return String(localized: "In Progress")
        case .finished:
            return String(localized: "Completed")
        }
    }

    var iconName: String {
        switch self {
        case .ongoing:
            return "target"
        case .finished:
            return "trophy.fill"
        }
    }
}

class AppGlobalStateManager: ObservableObject {
    static let shared = AppGlobalStateManager()
    @Published var showTaskListCreatNewTaskSheet: Bool = false
    @Published var selectedListType: TaskListType = .ongoing

    // 用于处理需要聚焦的任务项
    @Published var itemToFocus: PersistentIdentifier? = nil

    // 用于处理需要聚焦的集合项
    @Published var collectionToFocus: UUID? = nil

    /// 设置需要聚焦的任务项
    /// - Parameter taskId: 任务的 PersistentIdentifier
    func setItemToFocus(_ taskId: PersistentIdentifier) {
        itemToFocus = taskId
    }

    /// 清除聚焦项，防止重复执行
    func clearItemToFocus() {
        itemToFocus = nil
    }

    /// 设置需要聚焦的集合项
    /// - Parameter collectionId: 集合的 UUID
    func setCollectionToFocus(_ collectionId: UUID) {
        collectionToFocus = collectionId
    }

    /// 清除集合聚焦项，防止重复执行
    func clearCollectionToFocus() {
        collectionToFocus = nil
    }
}

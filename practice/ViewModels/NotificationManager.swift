//
//  NotificationManager.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//
import Foundation
import UserNotifications
import AVFoundation
import AudioToolbox
import CoreHaptics

class NotificationManager: ObservableObject {
    static let shared = NotificationManager()

    // 倒计时结束音效播放器
    private var countdownAudioPlayer: AVAudioPlayer?
    
    private var audioPlayer: AVAudioPlayer?

    @Published var dailyReminderEnabled: Bool {
          didSet {
              UserDefaults.standard.set(dailyReminderEnabled, forKey: "dailyReminderEnabled")
              if dailyReminderEnabled {
                  scheduleDailyReminder()
              } else {
                  cancelNotification(identifier: "dailyPracticeReminder")
              }
          }
      }


    @Published var reminderTime: Date {
        didSet {
            UserDefaults.standard.set(reminderTime, forKey: "reminderTime")
            if dailyReminderEnabled {
                scheduleDailyReminder()
            }
        }
    }

    // 倒计时提示音设置
    @Published var countdownSoundEnabled: Bool {
        didSet {
            UserDefaults.standard.set(countdownSoundEnabled, forKey: "countdownSoundEnabled")
        }
    }

    @Published var selectedRingtone: RingtoneOption {
        didSet {
            UserDefaults.standard.set(selectedRingtone.rawValue, forKey: "selectedRingtone")
        }
    }

    @Published var playInSilentMode: Bool {
        didSet {
            UserDefaults.standard.set(playInSilentMode, forKey: "playInSilentMode")
        }
    }

    // 铃声选项
    enum RingtoneOption: String, CaseIterable {
        case morning = "morning"
        case rise = "rise"
        case breeze = "breeze"
        case glow = "glow"

        var displayName: String {
            switch self {
            case .morning:
                return String(localized: "Morning")
            case .rise:
                return String(localized: "Rise")
            case .breeze:
                return String(localized: "Breeze")
            case .glow:
                return String(localized: "Glow")
            }
        }

        // 获取音频文件路径，用于预览播放
        var audioFileURL: URL? {
            switch self {
            case .morning:
                return Bundle.main.url(forResource: "morning", withExtension: "mp3")
            case .rise:
                return Bundle.main.url(forResource: "rise", withExtension: "mp3")
            case .breeze:
                return Bundle.main.url(forResource: "breeze", withExtension: "mp3")
            case .glow:
                return Bundle.main.url(forResource: "glow", withExtension: "mp3")
            }
        }
    }
    

    private init() {
        self.dailyReminderEnabled = UserDefaults.standard.bool(forKey: "dailyReminderEnabled")
        // 初始化倒计时提示音设置
        self.countdownSoundEnabled = UserDefaults.standard.object(forKey: "countdownSoundEnabled") as? Bool ?? true
        self.playInSilentMode = UserDefaults.standard.object(forKey: "playInSilentMode") as? Bool ?? false

        if let savedRingtone = UserDefaults.standard.string(forKey: "selectedRingtone"),
           let ringtone = RingtoneOption(rawValue: savedRingtone) {
            self.selectedRingtone = ringtone
        } else {
            self.selectedRingtone = .morning
        }

        if let savedTime = UserDefaults.standard.object(forKey: "reminderTime") as? Date {
            self.reminderTime = savedTime
        } else {
            var components = DateComponents()
            components.hour = 20
            components.minute = 0
            self.reminderTime = Calendar.current.date(from: components) ?? Date()
            UserDefaults.standard.set(self.reminderTime, forKey: "reminderTime")
        }

        checkNotificationAuthorizationStatus()
    }

    func checkNotificationAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                if settings.authorizationStatus != .authorized {
                    self.dailyReminderEnabled = false
                    self.countdownSoundEnabled = false
                }
            }
        }
    }
    
    func requestNotificationPermission(completion: @escaping (Bool) -> Void) {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            DispatchQueue.main.async {
                completion(granted)
                if !granted {
                    print("Notification permission denied: \(String(describing: error))")
                }
            }
        }
    }
    
    func scheduleDailyReminder() {
        cancelNotification(identifier: "dailyPracticeReminder")
        
        guard dailyReminderEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Practice Reminder"
        content.body = "It's time for your daily practice session!"
        content.sound = .default
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: reminderTime)
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "dailyPracticeReminder",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    func scheduleTrialEndingReminder(on date: Date) {
        cancelNotification(identifier: "trialEndingReminder")
        
        let content = UNMutableNotificationContent()
        content.title = "Trial Ending Soon"
        content.body = "Your free trial is almost over. Consider subscribing or cancel to avoid charges."
        content.sound = .default

        let triggerDate = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: false)

        let request = UNNotificationRequest(
            identifier: "trialEndingReminder",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request)
    }
    
    func cancelNotification(identifier: String) {
         UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
     }

     func cancelAllNotifications() {
         UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
     }

     // MARK: - 倒计时通知管理

     func scheduleCountdownNotification(identifier: String, title: String, body: String, timeInterval: TimeInterval) {
         guard countdownSoundEnabled else {
             print("❌ Countdown sound disabled, skipping notification")
             return
         }

         cancelNotification(identifier: identifier)

         let content = UNMutableNotificationContent()
         content.title = title
         content.body = body
         content.sound = .defaultCritical
         content.interruptionLevel = .timeSensitive

         let trigger = UNTimeIntervalNotificationTrigger(timeInterval: timeInterval, repeats: false)
         let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

         print("📅 Scheduling notification for \(timeInterval) seconds from now")

         UNUserNotificationCenter.current().add(request) { error in
             if let error = error {
                 print("❌ Error scheduling countdown notification: \(error)")
                 print("   Error type: \(type(of: error))")
                 print("   Error details: \(error.localizedDescription)")
             } else {
                 print("✅ Countdown notification scheduled successfully")
             }
         }
     }

     func cancelCountdownNotification(identifier: String) {
         UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: [identifier])
         UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
     }

     // MARK: - 调试方法

//     func checkPendingNotifications() {
//         UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
//             print("📋 Pending notifications count: \(requests.count)")
//             for request in requests {
//                 print("📋 Pending notification:")
//                 print("   - Identifier: \(request.identifier)")
//                 print("   - Title: \(request.content.title)")
//                 print("   - Body: \(request.content.body)")
//                 print("   - Sound: \(request.content.sound?.description ?? "nil")")
//
//                 if let trigger = request.trigger as? UNTimeIntervalNotificationTrigger {
//                     print("   - Time interval: \(trigger.timeInterval) seconds")
//                     print("   - Repeats: \(trigger.repeats)")
//                     let fireDate = Date().addingTimeInterval(trigger.timeInterval)
//                     print("   - Will fire at: \(fireDate)")
//                 }
//             }
//         }
//     }

//     func checkDeliveredNotifications() {
//         UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
//             print("📬 Delivered notifications count: \(notifications.count)")
//             for notification in notifications {
//                 print("📬 Delivered notification:")
//                 print("   - Identifier: \(notification.request.identifier)")
//                 print("   - Title: \(notification.request.content.title)")
//                 print("   - Delivery date: \(notification.date)")
//             }
//         }
//     }

//     func checkSystemNotificationSettings() {
//         UNUserNotificationCenter.current().getNotificationSettings { settings in
//             print("🔍 Detailed notification settings:")
//             print("   - Authorization status: \(self.authorizationStatusString(settings.authorizationStatus))")
//             print("   - Alert setting: \(self.notificationSettingString(settings.alertSetting))")
//             print("   - Sound setting: \(self.notificationSettingString(settings.soundSetting))")
//             print("   - Badge setting: \(self.notificationSettingString(settings.badgeSetting))")
//             print("   - Critical alert setting: \(self.notificationSettingString(settings.criticalAlertSetting))")
//             print("   - Notification center setting: \(self.notificationSettingString(settings.notificationCenterSetting))")
//             print("   - Lock screen setting: \(self.notificationSettingString(settings.lockScreenSetting))")
//             print("   - Car play setting: \(self.notificationSettingString(settings.carPlaySetting))")
//             print("   - Announcement setting: \(self.notificationSettingString(settings.announcementSetting))")
//             print("   - Scheduled delivery setting: \(self.notificationSettingString(settings.scheduledDeliverySetting))")
//             print("   - Time sensitive setting: \(self.notificationSettingString(settings.timeSensitiveSetting))")
//
//             if settings.authorizationStatus != .authorized {
//                 print("⚠️ Notifications not authorized!")
//             }
//             if settings.soundSetting != .enabled {
//                 print("⚠️ Sound setting not enabled!")
//             }
//             if settings.criticalAlertSetting != .enabled {
//                 print("⚠️ Critical alert setting not enabled!")
//             }
//         }
//     }

//     private func authorizationStatusString(_ status: UNAuthorizationStatus) -> String {
//         switch status {
//         case .notDetermined: return "notDetermined"
//         case .denied: return "denied"
//         case .authorized: return "authorized"
//         case .provisional: return "provisional"
//         case .ephemeral: return "ephemeral"
//         @unknown default: return "unknown"
//         }
//     }

//     private func notificationSettingString(_ setting: UNNotificationSetting) -> String {
//         switch setting {
//         case .notSupported: return "notSupported"
//         case .disabled: return "disabled"
//         case .enabled: return "enabled"
//         @unknown default: return "unknown"
//         }
//     }

     // MARK: - 直接音频播放（绕过通知系统）

    func playCountdownFinishedSound(at time: TimeInterval) {
         print("🎵 playCountdownFinishedSound called")
         print("   - countdownSoundEnabled: \(countdownSoundEnabled)")
         print("   - playInSilentMode: \(playInSilentMode)")
         print("   - selectedRingtone: \(selectedRingtone.rawValue)")

         guard countdownSoundEnabled else {
             print("❌ Countdown sound disabled, skipping")
             return
         }

         // 停止之前的音频播放
         countdownAudioPlayer?.stop()

         // 获取音频文件 URL
         guard let audioURL = selectedRingtone.audioFileURL else {
             print("❌ No audio file found for \(selectedRingtone.rawValue)")
             // 如果没有自定义音效，播放系统音效
             playSystemSound()
             return
         }

         print("✅ Found audio file: \(audioURL.path)")

         do {
             // 配置音频会话
             try configureAudioSession()
             print("✅ ConfigureAudioSession success")

             // 创建音频播放器
             countdownAudioPlayer = try AVAudioPlayer(contentsOf: audioURL)
             countdownAudioPlayer?.numberOfLoops = 2 // 播放3次（0 = 播放1次，1 = 播放2次，2 = 播放3次）
             countdownAudioPlayer?.volume = 1.0

             // 准备播放
             guard let player = countdownAudioPlayer else {
                 print("❌ Failed to create audio player")
                 return
             }

             let prepared = player.prepareToPlay()
             print("🎵 Audio player prepared: \(prepared)")

             // 开始播放
             let started = player.play(atTime: player.deviceCurrentTime + time)
             print("🎵 Audio playback started: \(started)")

             if started {
                 print("🎉 Successfully started playing countdown finished sound")
                 print("   - Duration: \(player.duration) seconds")
                 print("   - Will play \(player.numberOfLoops + 1) times")
             } else {
                 print("❌ Failed to start audio playback")
                 playSystemSound()
             }

         } catch {
             print("❌ Error playing countdown sound: \(error)")
             print("   Error details: \(error.localizedDescription)")
             // 回退到系统音效
             playSystemSound()
         }
     }

     private func configureAudioSession() throws {
         let audioSession = AVAudioSession.sharedInstance()

         print("🔧 Configuring audio session...")
         print("   - playInSilentMode: \(playInSilentMode)")

         if playInSilentMode {
             // 静音模式下也要播放：使用 .playback 类别
             try audioSession.setCategory(.playAndRecord, mode: .default, options: [.duckOthers, .defaultToSpeaker])
             print("✅ Audio session configured for playback (ignores silent switch)")
         } else {
             // 正常模式：使用 .ambient 类别，遵循静音开关
             try audioSession.setCategory(.ambient, mode: .default, options: [.duckOthers, .defaultToSpeaker])
             print("✅ Audio session configured for ambient (respects silent switch)")
         }

         try audioSession.setActive(true)
         print("✅ Audio session activated")
     }

     private func playSystemSound() {
         print("🔔 Playing system sound as fallback")
         if playInSilentMode {
             // 静音模式下播放系统 critical 音效
             AudioServicesPlaySystemSound(kSystemSoundID_Vibrate) // 先震动
             AudioServicesPlaySystemSound(1005) // 系统提示音（会忽略静音开关）
         } else {
             // 正常模式播放普通系统音效
             AudioServicesPlaySystemSound(1004) // 普通系统提示音
         }
     }

     func stopCountdownSound() {
         print("🛑 Stopping countdown sound")
         countdownAudioPlayer?.stop()
         countdownAudioPlayer = nil

         // 恢复音频会话
         do {
             try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
             print("✅ Audio session deactivated")
         } catch {
             print("⚠️ Error deactivating audio session: \(error)")
         }
     }

     func playTestSound() {
         // 停止当前播放的音频
         audioPlayer?.stop()

        if let audioURL = selectedRingtone.audioFileURL {
             // 对于自定义音频文件，直接播放
             do {
                 // 设置音频会话，允许在静音模式下播放（用于测试）
                 try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.duckOthers])
                 try AVAudioSession.sharedInstance().setActive(true)

                 audioPlayer = try AVAudioPlayer(contentsOf: audioURL)
                 audioPlayer?.play()
             } catch {
                 print("Error playing test sound: \(error)")
             }
         }
     }
}

import Foundation
import StoreKit

@MainActor
class MembershipManager: ObservableObject {
    static var shared = MembershipManager()
    
    // 订阅状态
    enum MembershipStatus: String {
        case free = "Free"
        case monthly = "Monthly"
        case yearly = "Yearly"
        case lifetime = "Lifetime"
        
        var localizedString: String {
            switch self {
            case .free:
                return String(localized: "Free User")
            case .monthly:
                return String(localized: "Monthly Member")
            case .yearly:
                return String(localized: "Yearly Member")
            case .lifetime:
                return String(localized: "Lifetime Member")
            }
        }

        var description: String {
            switch self {
            case .free:
                return String(localized: "You’re currently using the free plan — enjoy all features with up to 10 tasks.")
            case .monthly:
                return String(localized: "Thanks for supporting us! You’re a Monthly Pro member with unlimited access.")
            case .yearly:
                return String(localized: "You’re a Yearly Pro member — a smart and steady choice. Happy practicing!")
            case .lifetime:
                return String(localized: "You’re a Lifetime Pro member. Huge thanks for your support!")
            }
        }
    }

    let limitTaskCount = 10
    
    // 可购买的产品
    @Published private(set) var products: [Product] = []
    
    // 用户当前的订阅状态
    @Published private(set) var status: MembershipStatus = .free
    
    // 订阅事务更新的任务
    private var transactionTask: Task<Void, Error>?
    // 产品请求更新的任务
    private var productsTask: Task<Void, Error>?
    // 状态更新任务
    private var statusTask: Task<Void, Error>?
    
    // 产品ID常量
    let monthlyProductID = "com.onlypractice.monthly"
    let yearlyProductID = "com.onlypractice.yearly"
    let lifetimeProductID = "com.onlypractice.lifetime"
    
    init() {
        // 启动时加载产品信息
        Task {
            await loadProducts()
        }
        
        // 监听交易更新
        transactionTask = listenForTransactions()
        
        // 更新当前用户的订阅状态
        statusTask = Task {
            await updateMembershipStatus()
        }
    }
    
    deinit {
        transactionTask?.cancel()
        productsTask?.cancel()
        statusTask?.cancel()
    }
    
    // 请求产品信息
    func loadProducts() async {
        productsTask?.cancel()
        
        do {
            // 请求所有产品信息
            let productIDs = [monthlyProductID, yearlyProductID, lifetimeProductID]
            products = try await Product.products(for: productIDs)
            objectWillChange.send()
            // 初始加载后更新订阅状态
            await updateMembershipStatus()
        } catch {
            print("Failed to load products: \(error)")
        }
    }
    func freeTrialDescription(for product: Product) async -> String? {
        guard let offer = product.subscription?.introductoryOffer else { return nil }
        
        guard let introEligibility = await product.subscription?.isEligibleForIntroOffer else {
            return nil
        }
        
        if !introEligibility {
            return nil
        }

        let value = offer.period.value
        let unit = offer.period.unit

        let unitDescription: String

        switch unit {
        case .day:
            unitDescription = value == 1 ? "day" : "days"
        case .week:
            unitDescription = value == 1 ? "week" : "weeks"
        case .month:
            unitDescription = value == 1 ? "month" : "months"
        case .year:
            unitDescription = value == 1 ? "year" : "years"
        default:
            return nil
        }

        return "\(value) \(unitDescription) free trial"
    }
    
    // 获取特定类型的产品
    func getProduct(productID: String) -> Product? {
        return products.first { $0.id == productID }
    }
    
    // 处理购买
    func purchase(product: Product) async throws -> Bool {
        let result = try await product.purchase()
        
        switch result {
        case .success(let verificationResult):
            // 验证交易
            switch verificationResult {
            case .verified(let transaction):
                // 处理成功的交易
                await transaction.finish()
                await updateMembershipStatus()
                return true
            case .unverified:
                // 交易无法验证
                return false
            }
        case .userCancelled:
            // 用户取消了购买
            return false
        case .pending:
            // 交易待处理
            return false
        @unknown default:
            return false
        }
    }
    
    // 监听交易更新
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            // 监听来自 App Store 的交易更新
            for await result in Transaction.updates {
                // 检查交易验证
                switch result {
                case .verified(let transaction):
                    // 处理成功验证的交易
                    await transaction.finish()
                    
                    // 更新用户的会员状态
                    await self.updateMembershipStatus()
                case .unverified:
                    // 无法验证的交易
                    print("Unverified transaction")
                }
            }
        }
    }
    
    // 更新用户的会员状态
    func updateMembershipStatus() async {
        do {
            // 检查所有交易
            var hasMonthly = false
            var hasYearly = false
            var hasLifetime = false
            
            // 获取所有已验证的交易
            for await result in Transaction.currentEntitlements {
                if case .verified(let transaction) = result {
                    // 根据产品ID设置相应的状态
                    switch transaction.productID {
                    case monthlyProductID:
                        if transaction.revocationDate == nil && !transaction.isUpgraded {
                            hasMonthly = true
                        }
                    case yearlyProductID:
                        if transaction.revocationDate == nil && !transaction.isUpgraded {
                            hasYearly = true
                        }
                    case lifetimeProductID:
                        if transaction.revocationDate == nil {
                            hasLifetime = true
                        }
                    default:
                        break
                    }
                }
            }
            
            // 根据检查结果更新状态
            if hasLifetime {
                status = .lifetime
            } else if hasYearly {
                status = .yearly
            } else if hasMonthly {
                status = .monthly
            } else {
                status = .free
            }
        } catch {
            print("Failed to update membership status: \(error)")
            status = .free
        }
    }
    
    // 恢复购买
    func restorePurchases() async {
        // 尝试恢复之前的购买
        do {
            try await AppStore.sync()
            await updateMembershipStatus()
        } catch {
            print("Failed to restore purchases: \(error)")
        }
    }
    
    // 判断用户是否为付费会员(任何类型)
    var isPremium: Bool {
        switch status {
        case .free:
            return false
        case .monthly, .yearly, .lifetime:
            return true
        }
    }
}


//
//  StorageProvider.swift
//  practice
//
//  Created by <PERSON> on 2025/4/22.
//

import Foundation

protocol RecordingStorageProvider {
    func urlForSaving(filename: String) -> URL
    func deleteFile(at relativePath: String) throws
    func resolveURL(for relativePath: String) -> URL
}

class ICloudStorageProvider: RecordingStorageProvider {
    private let manager = iCloudDocumentManager.shared
    
    func urlForSaving(filename: String) -> URL {
        return manager.urlForSaving(filename: filename)
    }
    
    func deleteFile(at relativePath: String) throws {
        let url = manager.urlForSaving(filename: relativePath)
        try FileManager.default.removeItem(at: url)
    }
    
    func resolveURL(for relativePath: String) -> URL {
        return manager.urlForSaving(filename: relativePath)
    }
}

class LocalStorageProvider: RecordingStorageProvider {
    private let fileManager = FileManager.default
    private let folderName = "Recordings"

    private var baseURL: URL {
        let url = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
            .appendingPathComponent(folderName)
        try? fileManager.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }
    
    func urlForSaving(filename: String) -> URL {
        return baseURL.appendingPathComponent(filename)
    }
    
    func deleteFile(at relativePath: String) throws {
        let url = baseURL.appendingPathComponent(relativePath)
        try fileManager.removeItem(at: url)
    }
    
    func resolveURL(for relativePath: String) -> URL {
        return baseURL.appendingPathComponent(relativePath)
    }
}

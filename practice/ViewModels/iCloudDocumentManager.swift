//
//  iCloudDocumentManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/22.
//

import SwiftUI

class iCloudDocumentManager {
    static let shared = iCloudDocumentManager()
    
    private let fileManager = FileManager.default
    
    private let localFolderName = "Recordings"
    private var ubiquityURLCache: URL?
    
    private(set) var icloudAvailable: Bool = false
    
    private init() {
        self.icloudAvailable = checkICloudAvailability()
    }
    
    // MARK: - Public Methods
    
    /// 返回用于保存指定文件的路径（优先 iCloud）
    func urlForSaving(filename: String) -> URL {
        if icloudAvailable, let iCloudURL = iCloudDirectoryURL() {
            return iCloudURL.appendingPathComponent(filename)
        } else {
            return localDirectoryURL().appendingPathComponent(filename)
        }
    }
    
    /// 迁移本地所有文件到 iCloud
    func migrateLocalFilesToICloud(completion: ((Result<Int, Error>) -> Void)? = nil) {
        guard icloudAvailable, let iCloudURL = iCloudDirectoryURL() else {
            completion?(.failure(iCloudUnavailableError()))
            return
        }
        
        let localURL = localDirectoryURL()
        
        DispatchQueue.global().async {
            do {
                let files = try self.fileManager.contentsOfDirectory(at: localURL, includingPropertiesForKeys: nil)
                var migratedCount = 0
                
                for file in files {
                    let dest = iCloudURL.appendingPathComponent(file.lastPathComponent)
                    guard !self.fileManager.fileExists(atPath: dest.path) else { continue }
                    
                    try self.fileManager.copyItem(at: file, to: dest)
                    try self.fileManager.removeItem(at: file)
                    migratedCount += 1
                }
                
                DispatchQueue.main.async {
                    completion?(.success(migratedCount))
                }
            } catch {
                DispatchQueue.main.async {
                    completion?(.failure(error))
                }
            }
        }
    }
    
    // MARK: - Private Helpers
    
    private func checkICloudAvailability() -> Bool {
        return fileManager.url(forUbiquityContainerIdentifier: nil) != nil
    }
    
    /// 获取 iCloud 中的 App Documents 文件夹路径
    private func iCloudDirectoryURL() -> URL? {
        if let cached = ubiquityURLCache { return cached }
        
        guard let containerURL = fileManager.url(forUbiquityContainerIdentifier: nil) else {
            return nil
        }
        
        let folderURL = containerURL.appendingPathComponent("Documents").appendingPathComponent(localFolderName)
        
        do {
            try fileManager.createDirectory(at: folderURL, withIntermediateDirectories: true, attributes: nil)
            ubiquityURLCache = folderURL
            return folderURL
        } catch {
            print("创建 iCloud 文件夹失败: \(error)")
            return nil
        }
    }
    
    /// 获取本地 Documents 目录
    private func localDirectoryURL() -> URL {
        let url = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
            .appendingPathComponent(localFolderName)
        
        try? fileManager.createDirectory(at: url, withIntermediateDirectories: true, attributes: nil)
        return url
    }
    
    /// 自定义 iCloud 不可用错误
    private func iCloudUnavailableError() -> NSError {
        return NSError(domain: "iCloud", code: 1, userInfo: [NSLocalizedDescriptionKey: "iCloud 不可用"])
    }
    
    //    TODO: TEST 测试 debug 用
    func fileCount() -> Int {
        let directoryURL: URL
        if icloudAvailable, let iCloudURL = iCloudDirectoryURL() {
            directoryURL = iCloudURL
        } else {
            directoryURL = localDirectoryURL()
        }
        
        do {
            let files = try fileManager.contentsOfDirectory(at: directoryURL, includingPropertiesForKeys: nil)
            let audioFiles = files.filter { $0.pathExtension.lowercased() == "m4a" }
            return audioFiles.count
        } catch {
            print("读取文件失败: \(error)")
            return 0
        }
    }
    
    func getAllRecordingFiles() -> [URL] {
        let directoryURL: URL
        if icloudAvailable, let iCloudURL = iCloudDirectoryURL() {
            directoryURL = iCloudURL
        } else {
            directoryURL = localDirectoryURL()
        }
        
        do {
            let files = try fileManager.contentsOfDirectory(at: directoryURL, includingPropertiesForKeys: nil)
            let audioFiles = files.filter { $0.pathExtension.lowercased() == "m4a" }
            return audioFiles
        } catch {
            print("读取文件失败: \(error)")
            return []
        }
    }
    
}

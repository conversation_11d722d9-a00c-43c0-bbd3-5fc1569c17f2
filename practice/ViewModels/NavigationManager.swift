//
//  NavigationManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/20.
//

import SwiftUI
import SwiftData

enum NavigationPage: Hashable {
    case taskDetailPage(task: TaskItem)
    case practiceTimeDetailPage(task: TaskItem)
    case daySummaryPage
    case weekSummaryPage
    case monthSummaryPage
    case yearSummaryPage
    case collectionDetailPage(collection: CollectionItem)
    
    func hash(into hasher: inout Hasher) {
        switch self {
        case .taskDetailPage(let task):
            hasher.combine(0)
            hasher.combine(task.id)
        case .practiceTimeDetailPage(let task):
            hasher.combine(1)
            hasher.combine(task.id)
        case .daySummaryPage:
            hasher.combine(2)
        case .weekSummaryPage:
            hasher.combine(3)
        case .monthSummaryPage:
            hasher.combine(4)
        case .yearSummaryPage:
            hasher.combine(5)
        case .collectionDetailPage(let collection):
            hasher.combine(6)
            hasher.combine(collection.id)
        }
    }
    
    static func == (lhs: NavigationPage, rhs: NavigationPage) -> Bool {
        switch (lhs, rhs) {
        case (.taskDetailPage(let lhsTask), .taskDetailPage(let rhsTask)):
            return lhsTask.id == rhsTask.id
        case (.practiceTimeDetailPage(let lhsTask), .practiceTimeDetailPage(let rhsTask)):
            return lhsTask.id == rhsTask.id
        case (.daySummaryPage, .daySummaryPage):
            return true
        case (.weekSummaryPage, .weekSummaryPage):
            return true
        case (.monthSummaryPage, .monthSummaryPage):
            return true
        case (.yearSummaryPage, .yearSummaryPage):
            return true
        case (.collectionDetailPage(let lhsCollection), .collectionDetailPage(let rhsCollection)):
            return lhsCollection.id == rhsCollection.id
        default:
            return false
        }
    }
}

class NavigationManager: ObservableObject {
    static let shared = NavigationManager()
    @Published var path = [NavigationPage]()

    func pushState(item: NavigationPage) {
        path.append(item)
    }
    
    func popState() {
        if path.count > 0 {
            path.removeLast()
        }
    }
    
    func hasPage(item: NavigationPage) -> Bool {
        return path.contains(item)
    }
    
    static func getDestinationPageFrom(_ page: NavigationPage) -> some View {
        switch page {
        case .taskDetailPage(task: let task):
            return AnyView(TaskDetailView(task: task))
        case .practiceTimeDetailPage(task: let task):
            return AnyView(PracticeTimeEditView(task: task))
        case .daySummaryPage:
            return AnyView(DaySummaryView())
        case .weekSummaryPage:
            return AnyView(WeekSummaryView())
        case .monthSummaryPage:
            return AnyView(MonthSummaryView())
        case .yearSummaryPage:
            return AnyView(YearSummaryView())
        case .collectionDetailPage(collection: let collection):
            return AnyView(CollectionDetailView(collection: collection))
        }
    }
}

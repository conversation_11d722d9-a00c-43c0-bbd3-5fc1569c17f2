//
//  TaskListManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/15.
//

import SwiftUI
import SwiftData
import Combine

@MainActor
class TaskListManager: ObservableObject {
    private let modelContext: ModelContext = TaskItemSchemaV1.container.mainContext
    private lazy var imageManager = ImageManager(modelContext: modelContext)
    private lazy var recordingStorage = RecordingManager.defaultStorageProvider()
    static let shared = TaskListManager()

    @Published var taskItems: [TaskItem] = []
    @Published var taskProgress: [TaskProgress] = []

    // General purpose cache
    private var cache: [String: Any] = [:]
    
//    // Cache keys
//    private enum CacheKeys {
//        static let filteredTaskProgress = "filteredTaskProgress"
//        static let weekTaskProgress = "weekTaskProgress"
//        static let todayTaskProgress = "todayTaskProgress"
//        static let progressListByMonth = "progressListByMonth"
//        static let progressListByYear = "progressListByYear"
//        static let last7DaysPracticeTimeList = "last7DaysPracticeTimeList"
//        static let last14DaysPracticeTimeList = "last14DaysPracticeTimeList"
//        static let last30DaysPracticeTimeList = "last30DaysPracticeTimeList"
//        static let last100DaysPracticeTimeList = "last100DaysPracticeTimeList"
//        static let lastHalfYearPracticeTimeList = "lastHalfYearPracticeTimeList"
//        static let last365DaysPracticeTimeList = "last365DaysPracticeTimeList"
//        static let todayPracticeList = "todayPracticeList"
//        static let specificDatePracticeList = "specificDatePracticeList"
//        static let specificWeekPracticeList = "specificWeekPracticeList"
//        static let specificMonthPracticeList = "specificMonthPracticeList"
//        static let specificYearPracticeList = "specificYearPracticeList"
//        static let practiceListByUnit = "practiceListByUnit"
//        static let dailyPracticeTimeForWeek = "dailyPracticeTimeForWeek"
//        static let dailyPracticeTimeForMonth = "dailyPracticeTimeForMonth"
//        static let dailyPracticeTimeByRange = "dailyPracticeTimeByRange"
//        static let monthPracticeList = "monthPracticeList"
//        static let yearPracticeList = "yearPracticeList"
//        static let monthlyPracticeTimeForYear = "monthlyPracticeTimeForYear"
//        static let currentWeekProgress = "currentWeekProgress"
//        static let yesterdayProgress = "yesterdayProgress"
//    }
    
    init() {
        loadData()
    }
    
    var userCreateTaskCount: Int {
        taskItems.filter { item in item.taskType == .Achievement || item.taskType == .Ongoing }.count
    }

    func getPracticeTime() async -> Int {
        let total = taskProgress.reduce(0) { $0 + ($1.practiceTime ?? 0) }
        return total
    }

    func getOngoingTask() -> [TaskItem] {
        return taskItems.filter { item in item.taskType == .Ongoing }
    }
    
    func getOngoingAndDefaultTasks() -> [TaskItem] {
        return taskItems.filter { item in item.taskType == .Ongoing || item.taskType == .Default }
    }

    func getAchievementTask() -> [TaskItem] {
        return taskItems.filter { item in item.taskType == .Achievement }
    }

    func loadData() {
        do {
            let itemDescriptor = FetchDescriptor<TaskItem>(sortBy: [SortDescriptor(\.lastModified, order: .reverse)])
            let progressDescriptor = FetchDescriptor<TaskProgress>()
            taskItems = try modelContext.fetch(itemDescriptor)
            taskProgress = try modelContext.fetch(progressDescriptor)
            objectWillChange.send()
        } catch {
            print("Error fetching data: \(error)")
            taskItems = []
            taskProgress = []
        }
    }
    
    func loadDataAsync() async {
        do {
            // 在后台线程执行 fetch
            let context = modelContext
            let (items, progress): ([TaskItem], [TaskProgress]) = try await Task.detached {
                let itemDescriptor = FetchDescriptor<TaskItem>(sortBy: [SortDescriptor(\.lastModified, order: .reverse)])
                let progressDescriptor = FetchDescriptor<TaskProgress>()
                
                let items = try context.fetch(itemDescriptor)
                let progress = try context.fetch(progressDescriptor)
                return (items, progress)
            }.value
            // 回到主线程更新 @Published 属性
            await MainActor.run {
                self.taskItems = items
                self.taskProgress = progress
            }

        } catch {
            print("Error fetching data: \(error)")
            await MainActor.run {
                self.taskItems = []
                self.taskProgress = []
            }
        }
    }
    
    func getDefaultTask() -> TaskItem? {
        return taskItems.filter { item in item.taskType == .Default }.first
    }

    func getTaskById(id: String) -> TaskItem? {
        return taskItems.filter { item in item.id.uuidString == id }.first
    }
    
    func updatePracticeTime(of task: TaskItem, at date: Date, by seconds: Int) {
        let startOfDay = Calendar.current.startOfDay(for: date)
        if let progress = task.taskProgress?.first(where: { progress in
            return Calendar.current.isDate(progress.date, inSameDayAs: startOfDay)
        }) {
            progress.practiceTime = (progress.practiceTime ?? 0) + seconds
        } else {
            let progressItem = TaskProgress(
                date: date,
                status: .finished
            )
            progressItem.practiceTime = seconds
            task.taskProgress?.append(progressItem)
            task.lastModified = .now
        }
        try? modelContext.save()
        loadData()
    }


    // Auto Create One Default Task
    func autoCreateDefaultTask() {
        let defaultTasks = taskItems.filter { item in item.taskType == .Default }
        if defaultTasks.count == 0 {
            let defaultItem = TaskItem(
                pieceName: "Just Practice",
                composerName: "",
                key: .cMajor,
                difficulty: .one,
                beginDate: Date(),
                taskType: .Default
            )
            modelContext.insert(defaultItem)
            try? modelContext.save()
        }
        if defaultTasks.count > 1 {
            for index in 1...defaultTasks.count - 1 {
                defaultTasks[0].taskProgress?.append(contentsOf:  defaultTasks[index].taskProgress ?? [])

                let taskToDelete = defaultTasks[index]

                // 删除关联的图片文件
                if let imagePath = taskToDelete.coverImagePath {
                    imageManager.deleteImage(path: imagePath)
                }

                // 删除关联的录音文件
                if let recordings = taskToDelete.recordings {
                    for recording in recordings {
                        if !recording.fileUrl.isEmpty {
                            try? recordingStorage.deleteFile(at: recording.fileUrl)
                        }
                    }
                }

                // 删除关联的视频文件
                if let videos = taskToDelete.videos {
                    for video in videos {
                        if !video.fileUrl.isEmpty {
                            try? recordingStorage.deleteFile(at: video.fileUrl)
                        }
                    }
                }

                modelContext.delete(taskToDelete)
            }
        }
        loadData()
    }
    
    func createNewTask(_ task: TaskItem) {
        modelContext.insert(task)
        try? modelContext.save()
        loadData()
    }
    
    func generateTestData() {
        let composers = ["Bach", "Mozart", "Beethoven", "Chopin", "Debussy"]
        let pieces = ["Sonata", "Prelude", "Etude", "Nocturne", "Waltz"]
        let levels = Level.allCases
        let keys = MusicalKey.allCases
    
        for i in 0..<10 {
            let composer = composers.randomElement()!
            let piece = pieces.randomElement()!
            let level = levels.randomElement()!
            let key = keys.randomElement()!
            let date = Calendar.current.date(byAdding: .day, value: -Int.random(in: 0...30), to: Date())!

            let task = TaskItem(
                pieceName: "\(piece) No.\(i+1)",
                composerName: composer,
                key: key,
                difficulty: level,
                beginDate: date,
                taskType: .Ongoing
            )

            modelContext.insert(task)
        }
    }
    
    func deleteTask(taskItem: TaskItem) {
        // 删除关联的图片文件
        if let imagePath = taskItem.coverImagePath {
            imageManager.deleteImage(path: imagePath)
        }

        // 删除关联的录音文件
        if let recordings = taskItem.recordings {
            for recording in recordings {
                if !recording.fileUrl.isEmpty {
                    try? recordingStorage.deleteFile(at: recording.fileUrl)
                }
            }
        }

        // 删除关联的视频文件
        if let videos = taskItem.videos {
            for video in videos {
                if !video.fileUrl.isEmpty {
                    try? recordingStorage.deleteFile(at: video.fileUrl)
                }
            }
        }

        modelContext.delete(taskItem)
        loadData()
    }
    
    func finishTask(taskItem: TaskItem) {
        taskItem.taskType = .Achievement
        taskItem.finishedDate = Date()
        taskItem.lastModified = .now
        try? modelContext.save()
        loadData()
    }
    
    func getTodayPracticePieceList() async -> [(String, Int)] {
        let today = Date()
        return await getPracticePieceList(from: today, to: today)
    }
    
    func getWeekPracticeDateTimeList(_ weekStartDate: Date) async -> [(Date, Int)] {
        let calendar = Calendar.current
        let weekEndDate = calendar.date(byAdding: .day, value: 6, to: weekStartDate)!
        
        return await getPracticeDateTimeList(
            from: weekStartDate,
            to: weekEndDate,
            by: .day
        )
    }
    
    func getDailyPracticeTimeForMonth(_ monthDate: Date) async -> [(Date, Int)] {
        let calendar = Calendar.current
        
        // 获取月份的起始日期和结束日期
        let components = calendar.dateComponents([.year, .month], from: monthDate)
        guard let monthStartDate = calendar.date(from: components),
              let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStartDate),
              let monthEndDate = calendar.date(byAdding: .day, value: -1, to: nextMonth) else {
            return []
        }
        
        return await getPracticeDateTimeList(
            from: monthStartDate,
            to: monthEndDate,
            by: .day
        )
    }
    
    func getPracticePieceList(from startDate: Date, to endDate: Date) async -> [(String, Int)] {
        do {
            let descriptor = FetchDescriptor<TaskProgress>()
            let progressList = try modelContext.fetch(descriptor)

            let calendar = Calendar.current
            var result: [String: Int] = [:]

            for progress in progressList {
                guard progress.status == .finished,
                      let time = progress.practiceTime,
                      let task = progress.task else { continue }

                let date = calendar.startOfDay(for: progress.date)
                if date >= calendar.startOfDay(for: startDate) && date <= calendar.startOfDay(for: endDate) {
                    result[task.pieceName, default: 0] += time
                }
            }

            return result.sorted { $0.value > $1.value }
        } catch {
            print("Error fetching task progress: \(error)")
            return []
        }
    }
    
    func getPracticeDateTimeList(
        from startDate: Date,
        to endDate: Date,
        by component: Calendar.Component,
        for task: TaskItem? = nil
    ) async ->  [(Date, Int)] {
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: startDate)
        let end = calendar.startOfDay(for: endDate)

        do {
            let descriptor = FetchDescriptor<TaskProgress>()
            let taskProgress = try modelContext.fetch(descriptor)

            var dateBuckets: [Date: Int] = [:]

            // 初始化所有日期 bucket
            var current = start
            while current <= end {
                dateBuckets[current] = 0
                guard let next = calendar.date(byAdding: component, value: 1, to: current) else { break }
                current = next
            }

            for progress in taskProgress {
                guard progress.status == .finished,
                      let practiceTime = progress.practiceTime else { continue }

                if let targetTask = task, progress.task !== targetTask {
                    continue
                }

                // 按 component 计算所属时间段
                let bucketDate: Date
                switch component {
                case .day:
                    bucketDate = calendar.startOfDay(for: progress.date)
                case .month:
                    if let date = calendar.date(from: calendar.dateComponents([.year, .month], from: progress.date)) {
                        bucketDate = date
                    } else {
                        continue
                    }
                case .year:
                    if let date = calendar.date(from: calendar.dateComponents([.year], from: progress.date)) {
                        bucketDate = date
                    } else {
                        continue
                    }
                default:
                    continue
                }

                if bucketDate >= start && bucketDate <= end {
                    dateBuckets[bucketDate, default: 0] += practiceTime
                }
            }

            return dateBuckets.sorted(by: { $0.key < $1.key }).map { ($0.key, $0.value) }
        } catch {
            print("Error fetching task progress: \(error)")
            return []
        }
    }
    
    //  MARK: Streak
    func isOnFire() async -> Bool {
        let descriptor = FetchDescriptor<TaskProgress>() // 可加谓词提升效率
        do {
            let results = try modelContext.fetch(descriptor)
            let calendar = Calendar.current
            let today = Date()
            guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else { return false }

            return results.contains {
                $0.status == .finished &&
                (calendar.isDate($0.date, inSameDayAs: today) ||
                 calendar.isDate($0.date, inSameDayAs: yesterday))
            }
        } catch {
            print("fetch error: \(error)")
            return false
        }
    }
    
    func getCurrentPracticeStreak() -> Int {
        // TODO
        let datesSet = Set(finishedPracticeDatesSorted())
        guard !datesSet.isEmpty else { return 0 }

        var streak = 0
        
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var currentDate = calendar.date(byAdding: .day, value: -1, to: today)!
        
        while datesSet.contains(currentDate) {
            streak += 1
            guard let previousDay = calendar.date(byAdding: .day, value: -1, to: currentDate) else {
                break
            }
            currentDate = previousDay
        }
        
        if datesSet.contains(today) {
            streak += 1
        }
        
        return streak
    }
    
    func getLongestPracticeStreak() -> Int {
        // TODO
        let dates = finishedPracticeDatesSorted()
        guard !dates.isEmpty else { return 0 }

        var longest = 1
        var currentStreak = 1

        for i in 1..<dates.count {
            let prev = dates[i - 1]
            let current = dates[i]
            if Calendar.current.isDate(current, inSameDayAs: Calendar.current.date(byAdding: .day, value: 1, to: prev)!) {
                currentStreak += 1
                longest = max(longest, currentStreak)
            } else if !Calendar.current.isDate(current, inSameDayAs: prev) {
                currentStreak = 1
            }
        }

        return longest
    }
    
    
    private func finishedPracticeDatesSorted() -> [Date] {
        let finishedDates = taskProgress
            .filter { $0.status == .finished && ($0.practiceTime ?? 0) > 0 }
            .map { Calendar.current.startOfDay(for: $0.date) }

        return Array(Set(finishedDates)).sorted()
    }
    
    var currentWeekProgress: [TaskProgress] {
        do {
            let filteredResults = taskProgress.filter { progressItem in
                return progressItem.status == .finished && isDateInCurrentWeek(progressItem.date)
            }
            return filteredResults
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    func getMonthPracticedDateAt(date: Date) -> [Date] {
        let calendar = Calendar.current
        let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: date))!
        let endOfMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth)!
        let dates = taskProgress.filter { progressItem in
            return progressItem.status == .finished && (progressItem.practiceTime ?? 0 > 0) && progressItem.date >= startOfMonth && progressItem.date < endOfMonth
        }
        return dates.map { Calendar.current.startOfDay(for: $0.date) }
    }

    // MARK: - Statistics Methods (migrated from TaskProgressData)

    func getMonthlyPracticeTimeForYear(_ yearDate: Date) async -> [(Date, Int)] {
        let calendar = Calendar.current
        let year = calendar.component(.year, from: yearDate)
        var monthDates: [Date] = []

        // Generate first day of each month for the year
        for month in 1...12 {
            if let date = calendar.date(from: DateComponents(year: year, month: month, day: 1)) {
                monthDates.append(date)
            }
        }

        return await getPracticeDateTimeList(
            from: monthDates.first ?? yearDate,
            to: monthDates.last ?? yearDate,
            by: .month
        )
    }

    func getPracticeListForYear(_ yearDate: Date) async -> [(String, Int)] {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year], from: yearDate)
        guard let yearStartDate = calendar.date(from: components),
              let nextYear = calendar.date(byAdding: .year, value: 1, to: yearStartDate),
              let yearEndDate = calendar.date(byAdding: .day, value: -1, to: nextYear) else {
            return []
        }

        return await getPracticePieceList(from: yearStartDate, to: yearEndDate)
    }

    func getPracticeListForMonth(_ monthDate: Date) async -> [(String, Int)] {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: monthDate)
        guard let monthStartDate = calendar.date(from: components),
              let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStartDate),
              let monthEndDate = calendar.date(byAdding: .day, value: -1, to: nextMonth) else {
            return []
        }

        return await getPracticePieceList(from: monthStartDate, to: monthEndDate)
    }

    func getPracticeListForDate(_ date: Date) async -> [(String, Int)] {
        return await getPracticePieceList(from: date, to: date)
    }

    func getPracticeListForWeek(_ weekStartDate: Date) async -> [(String, Int)] {
        let calendar = Calendar.current
        let weekEndDate = calendar.date(byAdding: .day, value: 6, to: weekStartDate) ?? weekStartDate

        return await getPracticePieceList(from: weekStartDate, to: weekEndDate)
    }

    func getDailyPracticeTimeForWeek(_ weekStartDate: Date) async -> [(Date, Int)] {
        return await getWeekPracticeDateTimeList(weekStartDate)
    }
}

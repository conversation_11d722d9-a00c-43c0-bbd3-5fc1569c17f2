//
//  RatingManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/15.
//


import Foundation
import StoreKit
import UIKit

final class RatingManager: ObservableObject {
    static let shared = RatingManager()

    private let launchCountKey = "rating_action_count"
    private let hasRatedKey = "has_user_rated"
    private let hasDeclinedKey = "has_user_declined_rating"
    private let triggerThreshold = 10

    @Published var shouldShowRatingRequest = false

    private init() {}

    /// 每当用户执行一次指定操作时调用
    func recordAction() {
        guard !hasRated, !hasDeclined else { return }

        var count = UserDefaults.standard.integer(forKey: launchCountKey)
        count += 1
        UserDefaults.standard.set(count, forKey: launchCountKey)

        print("recordAction", count, hasDeclined, hasRated)
        if count >= triggerThreshold {
            requestReviewIfAppropriate()
        }
    }

    /// 弹出系统评分弹窗（仅在 App Store 安装的正式版生效）
    private func requestReviewIfAppropriate() {
        guard let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene else { return }

        // 弹出系统评分弹窗
        SKStoreReviewController.requestReview(in: scene)

        // 标记为用户已经看到评分弹窗（无论是否评分）
        UserDefaults.standard.set(true, forKey: hasDeclinedKey)
    }

    // 公共状态属性
    var hasRated: Bool {
        return UserDefaults.standard.bool(forKey: hasRatedKey)
    }

    var hasDeclined: Bool {
        return UserDefaults.standard.bool(forKey: hasDeclinedKey)
    }

//    /// 如果你有 App 内“去评分”的按钮，点击后可以调用这个方法打开 App Store 并标记为已评分
    func openAppStoreAndMarkRated(appID: String) {
        if let url = URL(string: "https://apps.apple.com/app/id\(appID)?action=write-review") {
            UIApplication.shared.open(url)
            UserDefaults.standard.set(true, forKey: hasRatedKey)
        }
    }

}


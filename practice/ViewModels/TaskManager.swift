//
//  TaskManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/20.
//

import SwiftUI
import SwiftData

class TaskManager: ObservableObject {
    let task: TaskItem
    private let modelContext: ModelContext
    @Published var taskProgress: [TaskProgress] = []
    
    init(task: TaskItem, modelContext: ModelContext) {
        self.task = task
        self.modelContext = modelContext
    }

    func loadData() {
        taskProgress = task.taskProgress ?? []
    }
    
    func deleteTaskProgress(at date: Date) {
        
    }
}


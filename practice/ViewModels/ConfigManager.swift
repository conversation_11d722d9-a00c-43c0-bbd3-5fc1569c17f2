//
//  ConfigManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/15.
//


import SwiftUI
import SwiftData

@MainActor
class ConfigManager: ObservableObject {
    static let shared = ConfigManager()
    
    private let modelContext = TaskItemSchemaV1.container.mainContext
    
    @Published var personalConfig: PersonalConfig?
    
    init() {
        loadConfig()
        checkConfig()
    }

    func checkConfig() {
        if personalConfig == nil {
            createDefaultConfig()
        }
        cleanupDuplicateConfigs()
    }
    
    func loadConfig() {
        do {
            let configs = try modelContext.fetch(FetchDescriptor<PersonalConfig>())
            personalConfig = configs.first
        } catch {
            print("Error fetching configs: \(error)")
        }
    }
    
    // return Minute
    func getDailyGoal() -> Int {
        if let config = personalConfig {
            return (config.dailyPracticeGoal ?? 0) / 60
        }
        return 0
    }
    
    func updateDailyGoal(_ minutes: Int) {
        if let config = personalConfig {
            config.dailyPracticeGoal = minutes * 60
            try? modelContext.save()
        }
    }
    
    func createDefaultConfig() {
        if personalConfig == nil {
            let defaultConfig = PersonalConfig(dailyPracticeGoal: 30 * 60)
            modelContext.insert(defaultConfig)
            personalConfig = defaultConfig
        }
    }
    
    func cleanupDuplicateConfigs() {
        do {
            let configs = try modelContext.fetch(FetchDescriptor<PersonalConfig>())
            if configs.count > 1 {
                for index in 1..<configs.count {
                    modelContext.delete(configs[index])
                }
            }
        } catch {
            print("Error fetching configs: \(error)")
        }
    }
}

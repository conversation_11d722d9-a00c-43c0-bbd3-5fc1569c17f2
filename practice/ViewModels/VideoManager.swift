////
////  VideoManager.swift
////  practice
////
////  Created by <PERSON> on 2025/6/10.
////
//
//import Foundation
//import AVFoundation
//import SwiftData
//import Photos
//
//class VideoManager: NSObject, ObservableObject {
//    let storage: RecordingStorageProvider
//    private var modelContext: ModelContext
//    
//    @Published var isPlaying = false
//    @Published var playbackTime: TimeInterval = 0
//    @Published var status: VideoStatus = .notStart
//    
//    var lastPlayingURL: URL?
//    private var player: AVPlayer?
//    private var playerItemContext = 0
//    private var timeObserverToken: Any?
//    
//    enum VideoStatus: String {
//        case notStart
//        case inProgress
//        case finished
//    }
//    
//    static func defaultStorageProvider() -> RecordingStorageProvider {
//        let icloudManager = iCloudDocumentManager.shared
//        return icloudManager.icloudAvailable ? ICloudStorageProvider() : LocalStorageProvider()
//    }
//    
//    init(modelContext: ModelContext) {
//        self.modelContext = modelContext
//        self.storage = VideoManager.defaultStorageProvider()
//        super.init()
//    }
//    
//    func saveVideo(task: TaskItem, url sourceURL: URL) {
//        let formatter = DateFormatter()
//        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
//        let sanitizedTitle = task.pieceName
//            .replacingOccurrences(of: "[\\/:*?\"<>|]", with: "-", options: .regularExpression)
//        let dateString = formatter.string(from: Date())
//        let fileName = "\(sanitizedTitle)-\(dateString).mp4"
//        let destinationURL = storage.urlForSaving(filename: fileName)
//        do {
//            try FileManager.default.copyItem(at: sourceURL, to: destinationURL)
//            let video = VideoItem(
//                date: Date(),
//                fileUrl: "\(fileName)"
//            )
//            task.videos?.append(video)
//            modelContext.insert(video)
//            try? modelContext.save()
//            print("✅Saved video at: \(destinationURL.path), fileURL: \(fileName)")
//        } catch {
//            print("❌ Failed to save video: \(error)")
//        }
//    }
//    
//    func deleteVideo(_ video: VideoItem) {
//        do {
//            modelContext.delete(video)
//            try storage.deleteFile(at: video.fileUrl)
//            try? modelContext.save()
//            print("✅ Deleted video: \(video.fileUrl)")
//        } catch {
//            print("❌ Failed to delete video: \(error)")
//        }
//    }
//    
//    func playVideo(from url: URL) {
//        player = AVPlayer(url: url)
//        lastPlayingURL = url
//        isPlaying = true
//        player?.play()
//        addPeriodicTimeObserver()
//    }
//    
//    func stopPlayback() {
//        player?.pause()
//        isPlaying = false
//        playbackTime = 0
//        removePeriodicTimeObserver()
//    }
//    
//    private func addPeriodicTimeObserver() {
//        removePeriodicTimeObserver()
//        guard let player = player else { return }
//        let interval = CMTime(seconds: 0.1, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
//        timeObserverToken = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
//            self?.playbackTime = time.seconds
//        }
//    }
//    
//    private func removePeriodicTimeObserver() {
//        if let token = timeObserverToken, let player = player {
//            player.removeTimeObserver(token)
//            timeObserverToken = nil
//        }
//    }
//    
//    func resolveVideoURL(_ relativePath: String) -> URL {
//        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
//            .appendingPathComponent(relativePath)
//    }
//} 

//
//  CustomAlertView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/18.
//

import SwiftUI

struct CustomAlertView: View {
    var title: String
    var message: String
    var onDismiss: () -> Void

    var body: some View {
        Color(.clear)
            .ignoresSafeArea()
            .background(.ultraThinMaterial.opacity(0.9))
            .overlay(
                VStack(spacing: 16) {
                    Text(title)
                        .font(.title3)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .minimumScaleFactor(0.5)
                        .padding(.top)

                    Text(message)
                        .font(.subheadline)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)

                    Button("OK") {
                        onDismiss()
                    }
                    .buttonStyle(.plain)
                    .foregroundColor(.accent)
                    .cornerRadius(8)
                    .padding(.vertical)
                    
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(20)
                .shadow(radius: 10)
                .padding()
            )
    }
}
//#Preview {
//    CustomAlertView()
//}

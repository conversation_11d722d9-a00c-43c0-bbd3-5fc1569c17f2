//
//  ChildShape.swift
//  practice
//
//  Created by <PERSON> on 2025/5/18.
//

import SwiftUI

struct PreviewView: View {
    var body: some View {
        VStack {
            Spacer()
            CustomBackgroundShape()
                .fill(Color.red)
                .frame(height: 200)
            Spacer()
        }
    }
}

// Custom shape with a notch at the top
struct CustomBackgroundShape: Shape {
    func path(in rect: CGRect) -> Path {
        let cornerRadius: CGFloat = 20
        let bumpWidth: CGFloat = 60
        let bumpHeight: CGFloat = 20
        
        var path = Path()
        
        // Start from bottom-left
        path.move(to: CGPoint(x: 0, y: rect.height))
        path.addLine(to: CGPoint(x: 0, y: cornerRadius))
        path.addQuadCurve(to: CGPoint(x: cornerRadius, y: 0),
                          control: CGPoint(x: 0, y: 0))
        
        // Line to before the bump
        let bumpStartX = rect.midX - bumpWidth / 2
        path.addLine(to: CGPoint(x: bumpStartX, y: 0))
        
        // Add bump
        path.addQuadCurve(to: CGPoint(x: rect.midX, y: -bumpHeight),
                          control: CGPoint(x: bumpStartX, y: -bumpHeight))
        path.addQuadCurve(to: CGPoint(x: bumpStartX + bumpWidth, y: 0),
                          control: CGPoint(x: bumpStartX + bumpWidth, y: -bumpHeight))
        
        // Line to top-right corner
        path.addLine(to: CGPoint(x: rect.width - cornerRadius, y: 0))
        path.addQuadCurve(to: CGPoint(x: rect.width, y: cornerRadius),
                          control: CGPoint(x: rect.width, y: 0))
        
        path.addLine(to: CGPoint(x: rect.width, y: rect.height))
        path.closeSubpath()
        
        return path
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        PreviewView()
    }
}

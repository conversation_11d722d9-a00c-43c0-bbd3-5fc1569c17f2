//
//  WeekCalendarPicker.swift
//  practice
//
//  Created by <PERSON> on 2025/4/23.
//

import SwiftUI

struct WeekCalendarPicker: View {
    @Binding var selectedWeekStart: Date

    @State private var isPresented = false
    private let calendar = Calendar.current

    var body: some View {
        Button {
            isPresented = true
        } label: {
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.accentColor)
                Spacer()
                Text(weekDisplayText(for: selectedWeekStart))
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "chevron.down")
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 10)
            .padding(.horizontal)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $isPresented) {
            VStack {
                CalendarPicker(
                    selectedWeekStart: selectedWeekStart,
                    onSelectDate: { selectedDate in
                        if let weekStart = calendar.dateInterval(of: .weekOfYear, for: selectedDate)?.start {
                            selectedWeekStart = weekStart
                        }
//                        isPresented = false
                    }
                )
                .padding(.top)
                Spacer()
            }
            .background(.clear)
            .presentationDetents([.medium])
        }
    }

    func weekDisplayText(for date: Date) -> String {
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: date)
        let end = calendar.date(byAdding: .day, value: 6, to: start)!

        let sameYear = calendar.component(.year, from: start) == calendar.component(.year, from: end)

        let startFormatter = DateFormatter()
        let endFormatter = DateFormatter()

        startFormatter.locale = Locale.current
        endFormatter.locale = Locale.current

        if sameYear {
            startFormatter.setLocalizedDateFormatFromTemplate("yyyyMMMd") // e.g. "Apr 23, 2025"
            endFormatter.setLocalizedDateFormatFromTemplate("MMMd")       // e.g. "Apr 29"
        } else {
            startFormatter.setLocalizedDateFormatFromTemplate("yyyyMMMd")
            endFormatter.setLocalizedDateFormatFromTemplate("yyyyMMMd")
        }

        return "\(startFormatter.string(from: start)) – \(endFormatter.string(from: end))"
    }
}

struct CalendarPicker: View {
    var selectedWeekStart: Date
    var onSelectDate: (Date) -> Void
    @Environment(\.dismiss) private var dismiss

    private let calendar = Calendar.current
    private let today = Date()
    @State private var currentMonth: Date = Date()
    
    init(selectedWeekStart: Date, onSelectDate: @escaping (Date) -> Void) {
        self.selectedWeekStart = selectedWeekStart
        self.onSelectDate = onSelectDate

        // 初始化 currentMonth 为 selectedWeekStart 所在月份
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: selectedWeekStart)
        _currentMonth = State(initialValue: calendar.date(from: components) ?? Date())
    }

    var body: some View {
        VStack {
            HStack(alignment: .center) {
                Button {
                    currentMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth)!
                } label: {
                    Image(systemName: "arrowtriangle.backward.fill")
                }
                Spacer()
                Text(monthYearString(for: currentMonth))
                    .font(.headline)
                Spacer()
                Button {
                    currentMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth)!
                } label: {
                    Image(systemName: "arrowtriangle.forward.fill")
                }
            }
            .padding()

            let days = makeDays()

            LazyVGrid(columns: Array(repeating: GridItem(), count: 7)) {
                let weekdaySymbols = calendar.shortWeekdaySymbols

                ForEach(weekdaySymbols, id: \.self) { day in
                    Text(day)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .fontWeight(.medium)
                }

                ForEach(days, id: \.self) { date in
                    let isInCurrentMonth = calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)

                    Button {
                        onSelectDate(date)
//                        dismiss()
                    } label: {
                        Text("\(calendar.component(.day, from: date))")
                            .frame(width: 40, height: 40)
                            .fontWeight(.medium)
                            .foregroundColor(isInSelectedWeek(date) ? Color(.systemBackground) : isInCurrentMonth ? .primary : .gray.opacity(0.5)) // <- 🎯 这里设置颜色
                            .background(
                                isInSelectedWeek(date) ? Color.accent : Color.clear
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 20))
                    }
                }
            }
            .padding()
        }
    }

    private func isInSelectedWeek(_ date: Date) -> Bool {
        let start = calendar.startOfDay(for: selectedWeekStart)
        let end = calendar.date(byAdding: .day, value: 6, to: start)!
        return date >= start && date <= end
    }

    private func monthYearString(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter.string(from: date)
    }

    private func makeDays() -> [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else { return [] }

        let firstWeekday = calendar.component(.weekday, from: monthInterval.start)
        var dates: [Date] = []

        let offset = (firstWeekday + 6) % 7
        for i in 0..<offset {
            if let date = calendar.date(byAdding: .day, value: -offset + i, to: monthInterval.start) {
                dates.append(date)
            }
        }

        if let range = calendar.range(of: .day, in: .month, for: currentMonth) {
            for day in range {
                if let date = calendar.date(byAdding: .day, value: day - 1, to: monthInterval.start) {
                    dates.append(date)
                }
            }
        }

        while dates.count % 7 != 0 {
            if let last = dates.last,
               let next = calendar.date(byAdding: .day, value: 1, to: last) {
                dates.append(next)
            }
        }

        return dates
    }
}

#Preview {
    @Previewable @State var selectedWeekStart = Date()
    HStack {
        WeekCalendarPicker(selectedWeekStart: $selectedWeekStart)
    }
}

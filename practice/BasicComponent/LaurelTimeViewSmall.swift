//
//  LaurelTimeView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/17.
//
import SwiftUI

struct LaurelTimeViewSmall: View {
    let timeValue: String
    let timeUnit: String
    
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Image(systemName: "laurel.leading")
                .smallLaurelStyle()
                .rotationEffect(Angle(degrees: -20))
                .offset(x: 0, y: 5)
            VStack(spacing: 0) {
                Text(timeValue)
                    .font(.system(size: 30, weight: .bold, design: .rounded))
                    .lineLimit(1)
                    .minimumScaleFactor(0.3)
                Text(timeUnit)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .fontWeight(.bold)
                    .padding(.horizontal)
            }
            
            Image(systemName: "laurel.trailing")
                .smallLaurelStyle()
                .rotationEffect(Angle(degrees: 20))
                .offset(x: 0, y: 5)
        }
    }
}

private extension Image {
    func smallLaurelStyle() -> some View {
        self
            .font(.system(size: 30))
            .fontWeight(.bold)
    }
}
#Preview {
    HStack {
        LaurelTimeViewSmall(
            timeValue: "10",
            timeUnit: "hours"
        )
        .padding()
    }
}

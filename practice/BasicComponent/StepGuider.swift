//
//  StepGuider.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI

struct StepGuider<Content: View>: View {
    // Current step index
    @Binding var currentStep: Int
    
    // Total number of steps
    let totalSteps: Int
    
    // The fixed illustration to display
    let illustration: AnyView
    
    // Content builders for each step
    let stepContents: [Int: () -> Content]
    
    // Animation slide direction
    @State private var slideDirection: SlideDirection = .fromRight
    
    // Optional action to perform when user completes all steps
    var onComplete: (() -> Void)?
    
    // Optional skip action
    var onSkip: (() -> Void)?
    
    // Slide animation direction
    enum SlideDirection {
        case none, fromRight, fromLeft
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Top indicator dots
            ZStack {
                StepIndicator(currentStep: currentStep, totalSteps: totalSteps)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                if let onSkip = onSkip {
                    HStack {
                        Spacer()
                        Button("skip") {
                            onSkip()
                        }
                        .foregroundColor(.gray)
                        .padding(.trailing)
                    }
                }
            }
            
            // Fixed illustration - takes about 40% of the height
            illustration
                .frame(maxHeight: UIScreen.main.bounds.width * 0.5)
//                .padding(.vertical, 20)
            
            // Step content with sliding animation
            ZStack {
                if let contentBuilder = stepContents[currentStep] {
                    contentBuilder()
                        .transition(slideDirection == .fromRight ?
                            .asymmetric(
                                insertion: .move(edge: .trailing),
                                removal: .move(edge: .leading)
                            ) :
                                .asymmetric(
                                    insertion: .move(edge: .leading),
                                    removal: .move(edge: .trailing)
                                ))
                }
            }
            .animation(.easeInOut(duration: 0.3), value: currentStep)
            .frame(maxWidth: .infinity)
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            Spacer()
            
            // Next/Complete button
//            Button(action: {
//                if currentStep < totalSteps - 1 {
//                    slideDirection = .fromRight
//                    currentStep += 1
//                } else {
//                    onComplete?()
//                }
//            }) {
//                Text(currentStep < totalSteps - 1 ? "Next" : "Complete")
//                    .font(.headline)
//                    .foregroundColor(.white)
//                    .frame(maxWidth: .infinity)
//                    .padding()
//                    .background(Color.pink)
//                    .cornerRadius(28)
//                    .padding(.horizontal, 20)
//                    .padding(.bottom, 24)
//            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

struct StepIndicator: View {
    let currentStep: Int
    let totalSteps: Int
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<totalSteps, id: \.self) { index in
                Circle()
                    .fill(index == currentStep ? Color.gray.opacity(0.7) : Color.gray.opacity(0.3))
                    .frame(width: index == currentStep ? 16 : 8, height: 8)
                    .animation(.easeInOut, value: currentStep)
            }
        }
    }
}


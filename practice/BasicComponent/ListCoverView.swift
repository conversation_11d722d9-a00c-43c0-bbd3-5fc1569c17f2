//
//  ListCoverView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/12.
//

import SwiftUI

struct ListCoverView<Content: View, Background: View>: View {
    var title: String
    var subTitle: String
    var background: () -> Background
    var content: () -> Content
    
    @State private var scrollOffset: CGFloat = 0
    private let imageHeight: CGFloat = 300
    
    init(
        title: String,
        subTitle: String,
        @ViewBuilder background: @escaping () -> Background,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.title = title
        self.subTitle = subTitle
        self.background = background
        self.content = content
    }
    
    var body: some View {
        GeometryReader { geo in
            let frameHeight = min(imageHeight, geo.size.width)
            
            List {
                // Cover Section
                ZStack(alignment: .bottomLeading) {
                    background()
                        .frame(height: frameHeight)
                        .clipped()
                        .overlay(
                            Rectangle()
                                .fill(.black)
                                .opacity(max(0, scrollOffset) / (0.8 * frameHeight))
                        )
                    
                    VStack(alignment: .leading, spacing: 6) {
                        Text(title)
                            .font(.largeTitle)
                            .bold()
                        Text(subTitle)
                            .font(.subheadline)
                    }
                    .foregroundColor(.white)
                    .padding()
                }
                .listRowInsets(EdgeInsets())
                .background(
                    GeometryReader {
                        Color.clear.preference(
                            key: ListScrollOffsetKey.self,
                            value: -$0.frame(in: .named("scroll")).origin.y
                        )
                    }
                )
                
                // Main Content Section
                content()
                    .listRowInsets(EdgeInsets())
            }
            .listStyle(.plain)
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetKey.self) { value in
                scrollOffset = value
            }
            .navigationTitle(scrollOffset > (frameHeight - 50) ? title : "")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// Offset Key
private struct ListScrollOffsetKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value += nextValue()
    }
}

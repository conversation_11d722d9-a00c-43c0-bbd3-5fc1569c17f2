//
//  PlaybackSliderView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/17.
//

import SwiftUI

struct PlaybackSliderView: View {
    @Binding var sliderValue: Double
    let duration: TimeInterval
    let currentTime: TimeInterval
    var onSeek: (TimeInterval) -> Void

    @State private var isEditingSlider = false

    var body: some View {
        VStack {
            Slider(
                value: $sliderValue,
                in: 0...duration,
                onEditingChanged: { isEditing in
                    isEditingSlider = isEditing
                    if !isEditing {
                        onSeek(sliderValue)
                    }
                }
            )
            .accentColor(.accentColor)
            .onChange(of: currentTime) { newValue in
                if !isEditingSlider {
                    withAnimation(.default) {
                        sliderValue = newValue
                    }
                }
            }

            HStack {
                Text(timeString(from: currentTime))
                Spacer()
                Text(timeString(from: duration))
            }
            .font(.caption)
//            .foregroundColor(.secondary)
        }
    }
}


//#Preview {
//    PlaybackSliderView()
//}

//
//  ArcProgressView.swift
//  practice
//
//  Created by 陈昇 on 4/12/24.
//

import SwiftUI
import Foundation

struct ArcProgressView: View {
    @Binding var progress: CGFloat
    var color: Color = .accentColor
    var lineWidth: CGFloat = 10

    var body: some View {
        ZStack {
            ArcShape(progress: 1)
                .stroke(.secondary.opacity(0.1), style: StrokeStyle(lineWidth: lineWidth / 2, lineCap: .round))
            
            ArcShape(progress: progress)
                .stroke(color, style: StrokeStyle(lineWidth: lineWidth, lineCap: .round))
        }
    }
}

struct ArcShape: Shape {
    var progress: CGFloat
    
    var animatableData: CGFloat {
        get { progress }
        set { progress = newValue }
    }
    
    func path(in rect: CGRect) -> Path {
        let adjustedProgress = max(min(progress, 1), 0) // Clamp between 0 and 1
        let startAngle = Angle(degrees: -40)
        let endAngle = Angle(degrees: -40 + Double(adjustedProgress) * 260)
        var path = Path()
        
        path.addArc(center: CGPoint(x: rect.midX, y: rect.midY),
                    radius: rect.width / 2,
                    startAngle: startAngle,
                    endAngle: endAngle,
                    clockwise: false)
        
        return path
    }
}

//#Preview {
//    @Previewable @State var progress: CGFloat = 0.5
//    
//    return VStack {
//        ArcProgressView(progress: $progress, color: .accent)
//            .frame(width: 100, height: 100)
//            .rotationEffect(.degrees(180))
//        Button("Animate Progress") {
//            withAnimation(.easeInOut(duration: 0.3)) {
//                progress = CGFloat.random(in: 0...1)
//            }
//        }
//    }
//}

#Preview {
    var number = pow(0.1, 0.0)
    VStack {
        Text("\(getTimeString(of: 64780))")
        Text("\(number)")
    }
}

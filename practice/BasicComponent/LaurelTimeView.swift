//
//  LaurelTimeView.swift
//  practice
//
//  Created by <PERSON> on 2025/4/17.
//
import SwiftUI

struct LaurelTimeView: View {
    let timeValue: String
    let timeUnit: String
    
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Spacer()
            
            Image(systemName: "laurel.leading")
                .laurelStyle()
            
            VStack(spacing: 4) {
                Text(timeValue)
                    .font(.system(size: 80, weight: .bold, design: .rounded))
                    .foregroundColor(.accentColor)
                    .lineLimit(1)
                    .minimumScaleFactor(0.3)
                Text(timeUnit)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            Image(systemName: "laurel.trailing")
                .laurelStyle()
            
            Spacer()
        }
    }
}

private extension Image {
    func laurelStyle() -> some View {
        self
            .font(.system(size: 60))
            .fontWeight(.bold)
            .foregroundStyle(
                LinearGradient(
                    colors: [.orange, .yellow],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
    }
}
#Preview {
    HStack {
        LaurelTimeView(
            timeValue: "10000000",
            timeUnit: "seconds"
        )
        .padding()
    }
}

//
//  DayCalendarPicker.swift
//  practice
//
//  Created by <PERSON> on 2025/4/23.
//

import SwiftUI

struct DayCalendarPicker: View {
    @Binding var selectedDate: Date
    
    @State private var isPresented = false
    private let calendar = Calendar.current
    
    var body: some View {
        Button {
            isPresented = true
        } label: {
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.accentColor)
                Spacer()
                Text(dayDisplayText(for: selectedDate))
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "chevron.down")
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 10)
            .padding(.horizontal)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $isPresented) {
            VStack {
                DayPicker(selectedDate: $selectedDate, isPresented: $isPresented)
                    .padding(.top)
                Spacer()
            }
            .background(.clear)
            .presentationDetents([.medium])
        }
    }
    
    func dayDisplayText(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.setLocalizedDateFormatFromTemplate("yyyyMMMd") // e.g. "Apr 23, 2025"
        return formatter.string(from: date)
    }
}

struct DayPicker: View {
    @Binding var selectedDate: Date
    @Binding var isPresented: Bool
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            DatePicker(
                "",
                selection: $selectedDate,
                displayedComponents: [.date]
            )
            .datePickerStyle(.graphical)
            .padding(.horizontal)
            
            Spacer()
        }
    }
}

#Preview {
    @Previewable @State var selectedDate = Date()
    HStack {
        DayCalendarPicker(selectedDate: $selectedDate)
    }
}

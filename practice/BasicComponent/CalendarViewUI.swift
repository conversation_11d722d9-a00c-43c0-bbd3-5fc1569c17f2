import HorizonCalendar
import <PERSON><PERSON>

struct CalendarViewUI: View {
    // MARK: Private
    private let calendar: Calendar
    private let monthsLayout: MonthsLayout
    private let onDateSelect: (_ day: DayComponents) -> ()
    @ViewBuilder private let dayCellRender: (_ day: DayComponents) -> AnyView
    private let dependency: Any

    private let visibleDateRange: ClosedRange<Date>
    private let monthDateFormatter: DateFormatter
    
    @State private var selectedDate: Date?
    
    @StateObject private var calendarViewProxy = CalendarViewProxy()
    
    private func isDaySelected(_ day: DayComponents) -> Bool {
        if let selectedDate {
            return calendar.date(from: day.components) == selectedDate
        } else {
            return false
        }
    }
    
    // MARK: Lifecycle
    
    init(calendar: Calendar, monthsLayout: MonthsLayout, onDateSelect: @escaping (_: DayComponents) -> (), dayCellRender:  @escaping (_: DayComponents) -> AnyView, dependency: Any) {
        self.calendar = calendar
        self.monthsLayout = monthsLayout
        self.onDateSelect = onDateSelect
        self.dayCellRender = dayCellRender
        self.dependency = dependency
        
        let startDate = calendar.date(from: DateComponents(year: 2000, month: 01, day: 01))!
        let endDate = Date.now
        
        visibleDateRange = startDate...endDate
        monthDateFormatter = DateFormatter()
        monthDateFormatter.calendar = calendar
        monthDateFormatter.locale = calendar.locale
        monthDateFormatter.dateFormat = DateFormatter.dateFormat(
            fromTemplate: "MMMM yyyy",
            options: 0,
            locale: calendar.locale ?? Locale.current)
    }
    
    // MARK: Internal
    
    var body: some View {
        CalendarViewRepresentable(
            calendar: calendar,
            visibleDateRange: visibleDateRange,
            monthsLayout: monthsLayout,
            dataDependency: dependency,
            proxy: calendarViewProxy
        )
        .interMonthSpacing(24)
        .verticalDayMargin(8)
        .horizontalDayMargin(8)
        .monthHeaders { month in
            let monthHeaderText = monthDateFormatter.string(from: calendar.date(from: month.components)!)
            Group {
                if case .vertical = monthsLayout {
                    HStack {
                        Text(monthHeaderText)
                            .font(.title2)
                        Spacer()
                    }
                    .padding()
                } else {
                    Text(monthHeaderText)
                        .font(.title2)
                        .padding()
                }
            }
            .accessibilityAddTraits(.isHeader)
        }
        .days(dayCellRender)
        
        .onDaySelection { day in
            onDateSelect(day)
            selectedDate = calendar.date(from: day.components)
        }
        .onAppear {
            calendarViewProxy.scrollToDay(
                containing:  Date.now,
                scrollPosition: .centered,
                animated: false)
        }
        .frame(maxWidth: 375, maxHeight: .infinity)
    }
}

//struct SwiftUIDayView: View {
//
//  let dayNumber: Int
//  let isSelected: Bool
//
//  var body: some View {
//    ZStack(alignment: .center) {
//      Circle()
//        .strokeBorder(isSelected ? Color.accentColor : .clear, lineWidth: 2)
//        .background {
//          Circle()
//            .foregroundColor(isSelected ? Color(UIColor.systemBackground) : .clear)
//        }
//        .aspectRatio(1, contentMode: .fill)
//      Text("\(dayNumber)").foregroundColor(Color(UIColor.label))
//    }
//    .accessibilityAddTraits(.isButton)
//  }
//}

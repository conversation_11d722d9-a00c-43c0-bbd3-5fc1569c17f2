//
//  Waveform.swift
//  practice
//
//  Created by <PERSON> on 2025/4/16.
//

import SwiftUI

struct WaveformView: View {
    var powerLevels: [Float]
    let maxBarCount = 60

    var body: some View {
        GeometryReader { geo in
            let barWidth: CGFloat = 4
            let spacing: CGFloat = 3
            let prefixCount = max(0, maxBarCount - powerLevels.count)
            let displayLevels = Array(repeating: 0.0, count: prefixCount) + powerLevels.suffix(maxBarCount)

            HStack(alignment: .center, spacing: spacing) {
                ForEach(Array(displayLevels.enumerated()), id: \.offset) { index, level in
                    Capsule()
                        .fill(Color.primary.opacity(Double(index) / Double(maxBarCount)))
                        .frame(
                            width: barWidth,
                            height: CGFloat(max(level, 0)) * geo.size.height * 0.5
                        )
                }
            }
            .frame(width: geo.size.width, height: geo.size.height)
        }
        .frame(height: 60)
        .cornerRadius(5)
    }
}
//
//#Preview {
//    WaveformView()
//}

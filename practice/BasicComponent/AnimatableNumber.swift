//
//  AnimatableNumber.swift
//  practice
//
//  Created by <PERSON> on 2025/5/20.
//

import SwiftUI

struct AnimatableNumber: View {
    @Binding var number: Int
    @State private var rotation: Double = 0
    @State private var scale: CGFloat = 1.0
    @State private var colorChange: Double = 0
    
    var body: some View {
        Text("\(number)")
            .font(.system(size: 80, weight: .bold, design: .rounded))
            .foregroundColor(Color(
                hue: colorChange,
                saturation: 0.8,
                brightness: 0.8
            ))
            .rotation3DEffect(
                .degrees(rotation),
                axis: (x: 1, y: 0, z: 0)
            )
            .scaleEffect(scale)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: number)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: rotation)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: scale)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: colorChange)
            .onChange(of: number) { _, _ in
                // 添加旋转效果
                rotation = rotation == 0 ? 360 : 0
                
                // 添加缩放效果
                withAnimation(.spring(response: 0.2, dampingFraction: 0.5)) {
                    scale = 1.3
                }
                
                // 延迟后恢复正常大小
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        scale = 1.0
                    }
                }
                
                // 改变颜色
                colorChange = Double.random(in: 0...1)
            }
    }
}

struct AnimatableNumberContainer: View {
    @State private var number: Int = 4
    
    var body: some View {
        VStack(spacing: 30) {
            AnimatableNumber(number: $number)
            
            Button(action: {
                withAnimation {
                    number += 1
                }
            }) {
                Text("Add")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(width: 120)
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(Color.blue)
                    )
                    .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 3)
            }
        }
        .padding(50)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }
}

struct AnimatedNumberView: View {
    @State private var number: Int = 100
    @State private var blurRadius: CGFloat = 0
    @State private var opacity: Double = 1.0
    
    var body: some View {
        VStack {
            Text("\(number)")
                .font(.system(size: 60, weight: .bold, design: .rounded))
                .blur(radius: blurRadius)
//                .opacity(opacity)
                .animation(.easeInOut(duration: 0.2), value: blurRadius)
//                .animation(.easeInOut(duration: 0.3), value: opacity)
            
            Button("Change Number") {
//                withAnimation {
//                    blurRadius = 3
//                }
                
                // 延迟 0.3 秒，模糊之后再更新数字
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    
                    withAnimation(.smooth) {
                        number = Int.random(in: 100...999)
//                        blurRadius = 0
                    }
                }
            }
            .padding(.top, 40)
        }
    }
}

#Preview {
    AnimatedNumberView()
}

#  TODO

## Create Task
- Composer 自定义筛选 https://www.youtube.com/watch?v=ATgOV70YcI8

## Progress
- 查看每日的练习时长
- 查看每周

## 练习页面
- UI 优化：练习进度
- 展示今日练习内容
- 分享今日练习打卡

## 详情页面
- 完成任务动效

## System
- ✅ 多语言
https://developer.apple.com/documentation/Xcode/localizing-and-varying-text-with-a-string-catalog
- ✅ App 评分
https://www.hackingwithswift.com/quick-start/swiftui/how-to-ask-the-user-to-review-your-app


## Platform
- iPad
- MacOS

## 收费
- 订阅功能 https://developer.apple.com/in-app-purchase/

Privacy
https://apps4world.medium.com/solve-itms-91053-missing-api-declaration-60110545a8e0

## Explore

- Explore of other practice piece
- apple music
    - https://developer.apple.com/videos/play/wwdc2022/10148/
    - https://developer.apple.com/documentation/applemusicapi/search_for_catalog_resources

## Count Down
- ✅ 退出到后台，系统关闭程序，恢复倒计时状态
    - Restoring Your App’s State with SwiftUI https://developer.apple.com/documentation/swiftui/restoring_your_app_s_state_with_swiftui/
- ✅ 正计时
- ✅ bug fix: 计时 timer 不准确：widget 38min, timer 12min

## 数据展示
- 单日练习曲目时间体现
- 一周练习时间总结
- 本月总结
- 月练习 widget

## Widget
- 每日总结：
    - 总练习时间：xxx min
    - swift chart: 单个曲目练习时间（max 5）

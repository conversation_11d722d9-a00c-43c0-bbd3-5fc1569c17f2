//
//  MatchGeo.swift
//  practice
//
//  Created by <PERSON> on 2025/5/27.
//
import SwiftUI

struct MatchedGeometryExample: View {
    @Namespace private var animationNamespace
    @State private var isExpanded = false

    var body: some View {
        VStack {
            if isExpanded {
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.blue)
                    .frame(width: 300, height: 300)
                    .matchedGeometryEffect(id: "rect", in: animationNamespace)
                    .onTapGesture {
                        withAnimation(.bouncy) {
                            isExpanded.toggle()
                        }
                    }
            } else {
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.blue)
                    .frame(width: 100, height: 100)
                    .matchedGeometryEffect(id: "rect", in: animationNamespace)
                    .onTapGesture {
                        withAnimation {
                            isExpanded.toggle()
                        }
                    }
            }
        }
    }
}
#Preview {
    MatchedGeometryExample()
}

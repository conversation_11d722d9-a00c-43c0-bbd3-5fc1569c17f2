# 渲染优化文档

## 概述

为了解决当 tasks 数量过多时渲染出现黑色图片的问题，我们实现了一套完整的渲染优化方案。

## 主要问题

1. **内存压力**: 大量任务同时渲染导致内存不足
2. **图片缓存限制**: 超出缓存容量时频繁加载图片
3. **视图层次过深**: 复杂的视图结构超出 SwiftUI 渲染能力
4. **同步加载阻塞**: 大量图片同步加载造成主线程阻塞

## 解决方案

### 1. 简化渲染策略

```swift
// 根据任务数量选择渲染方式
if tasks.count <= 8 {
    renderDirectly(tasks: tasks, completion: completion)  // 直接渲染
} else {
    renderInBatchesHighQuality(tasks: tasks, completion: completion) // 分段渲染
}
```

### 2. 分段渲染机制

- **头部渲染**: 背景、标题、描述
- **任务批次渲染**: 每批3个任务（固定）
- **底部渲染**: 分享信息
- **图片合成**: 将所有部分合成最终图片

### 3. 高质量渲染

```swift
// 保持原始质量
renderer.scale = displayScale

// 失败时智能降级
if let uiImage = renderer.uiImage {
    completion(uiImage)
} else {
    renderer.scale = displayScale * 0.75
    completion(renderer.uiImage)
}
```

### 4. 异步图片预加载

```swift
// 预加载所有需要的图片
let imagePaths = tasks.compactMap { $0.coverImagePath }
await imageManager.preloadImages(paths: imagePaths, maxConcurrent: 3)
```

## 使用方法

### 基本使用

```swift
// 在 CollectionDetailView 中调用
render {
    showingShareView = true
}
```

### 监控渲染进度

```swift
// UI 中显示进度
if isRendering {
    ProgressView(value: renderProgress)
        .frame(width: 20, height: 20)
} else {
    Image(systemName: "square.and.arrow.up")
}
```

### 缓存管理

```swift
// 获取缓存信息
let cacheInfo = imageManager.getCacheInfo()
print("Cache: \(cacheInfo.count) images, \(cacheInfo.memoryUsage)")

// 清理缓存
imageManager.clearCache()
```

## 性能指标

| 任务数量 | 渲染方式 | 批次大小 | 预期时间 | 质量 |
|---------|---------|---------|---------|------|
| 1-8     | 直接渲染 | -       | < 2秒   | 原始质量 |
| 9-30    | 分段渲染 | 3个/批次 | 3-10秒  | 原始质量 |
| 30+     | 分段渲染 | 3个/批次 | 10-20秒 | 原始质量 |

## 最佳实践

1. **预加载关键图片**: 在渲染前预加载所有需要的图片
2. **监控内存使用**: 定期检查缓存状态，必要时清理
3. **用户反馈**: 显示渲染进度，提升用户体验
4. **错误处理**: 添加超时和失败重试机制

## 故障排除

### 渲染失败
- 检查图片文件是否存在
- 验证内存是否充足
- 查看控制台日志

### 渲染缓慢
- 减少批次大小
- 清理图片缓存
- 降低渲染质量

### 内存警告
- 调整缓存限制
- 增加批次间延迟
- 使用更小的图片尺寸

import Foundation
import UIKit
import SwiftData

class ImageManager {
    static let shared = ImageManager()
    
    private let fileManager = FileManager.default
    private var documentsDirectory: URL {
        fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
    }
    
    private init() {
        createImagesDirectoryIfNeeded()
    }
    
    // 创建图片目录
    private func createImagesDirectoryIfNeeded() {
        let imagesDirectory = documentsDirectory.appendingPathComponent("Images", isDirectory: true)
        if !fileManager.fileExists(atPath: imagesDirectory.path) {
            do {
                try fileManager.createDirectory(at: imagesDirectory, withIntermediateDirectories: true)
            } catch {
                print("创建图片目录失败: \(error)")
            }
        }
    }
    
    // 保存图片并返回文件名
    func saveImage(_ image: UIImage) -> String? {
        guard let data = image.jpegData(compressionQuality: 0.8) else { return nil }
        
        let filename = UUID().uuidString + ".jpg"
        let fileURL = documentsDirectory.appendingPathComponent("Images").appendingPathComponent(filename)
        
        do {
            try data.write(to: fileURL)
            return filename
        } catch {
            print("保存图片失败: \(error)")
            return nil
        }
    }
    
    // 从文件名加载图片
    func loadImage(fileName: String) -> UIImage? {
        let fileURL = documentsDirectory.appendingPathComponent("Images").appendingPathComponent(fileName)
        
        do {
            let data = try Data(contentsOf: fileURL)
            return UIImage(data: data)
        } catch {
            print("加载图片失败: \(error)")
            return nil
        }
    }
    
    // 删除图片
    func deleteImage(fileName: String) {
        let fileURL = documentsDirectory.appendingPathComponent("Images").appendingPathComponent(fileName)
        
        do {
            try fileManager.removeItem(at: fileURL)
        } catch {
            print("删除图片失败: \(error)")
        }
    }
    
    // 将图片数据迁移到文件系统
    func migrateImageData(_ imageData: Data) -> String? {
        guard let image = UIImage(data: imageData) else { return nil }
        return saveImage(image)
    }
} 
//
//  TaskProgressData.swift
//  practice
//
//  Created by 陈昇 on 5/29/24.
//

import SwiftData
import Foundation

//TODO 废弃
@available(*, deprecated, message: "Use TaskListManager instead. TaskProgressData will be removed in future versions.")
class TaskProgressData {
    // Singleton instance
    static let shared = TaskProgressData()
    
    // Private initializer to prevent instantiation
    private init() { }
    
    private var modelContainer = TaskItemSchemaV1.container
    
    // General purpose cache
    private var cache: [String: Any] = [:]
    
    // Cache keys
    private enum CacheKeys {
        static let filteredTaskProgress = "filteredTaskProgress"
        static let weekTaskProgress = "weekTaskProgress"
        static let last7DaysPracticeList = "last7DaysPracticeList"
        static let todayTaskProgress = "todayTaskProgress"
        static let progressListByMonth = "progressListByMonth"
        static let progressListByYear = "progressListByYear"
        static let last7DaysPracticeTimeList = "last7DaysPracticeTimeList"
        static let last14DaysPracticeTimeList = "last14DaysPracticeTimeList"
        static let last30DaysPracticeTimeList = "last30DaysPracticeTimeList"
        static let last100DaysPracticeTimeList = "last100DaysPracticeTimeList"
        static let lastHalfYearPracticeTimeList = "lastHalfYearPracticeTimeList"
        static let last365DaysPracticeTimeList = "last365DaysPracticeTimeList"
        static let todayPracticeList = "todayPracticeList"
        static let specificDatePracticeList = "specificDatePracticeList"
        static let specificWeekPracticeList = "specificWeekPracticeList"
        static let specificMonthPracticeList = "specificMonthPracticeList"
        static let specificYearPracticeList = "specificYearPracticeList"
        static let practiceListByUnit = "practiceListByUnit"
        static let dailyPracticeTimeForWeek = "dailyPracticeTimeForWeek"
        static let dailyPracticeTimeForMonth = "dailyPracticeTimeForMonth"
        static let dailyPracticeTimeByRange = "dailyPracticeTimeByRange"
        static let monthPracticeList = "monthPracticeList"
        static let yearPracticeList = "yearPracticeList"
        static let monthlyPracticeTimeForYear = "monthlyPracticeTimeForYear"
        static let currentWeekProgress = "currentWeekProgress"
        static let yesterdayProgress = "yesterdayProgress"
    }
    
    
    // Time unit for grouping practice data
    public enum TimeUnit: String {
        case day
        case week
        case month
        case year
    }

    @MainActor
    private var filteredTaskProgress: [TaskProgress] {
        if let cached = cache[CacheKeys.filteredTaskProgress] as? [TaskProgress] {
            return cached
        }

        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let filteredResults = results.filter { progressItem in
                progressItem.status == .finished
            }
            cache[CacheKeys.filteredTaskProgress] = filteredResults
            return filteredResults
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }

    @MainActor
    var weekTaskProgress: [TaskProgress] {
        if let cached = cache[CacheKeys.weekTaskProgress] as? [TaskProgress] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
//            TODO do it on background
            let results = try modelContainer.mainContext.fetch(progressList)
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && isDateInLastWeek(progressItem.date)
            }
            cache[CacheKeys.weekTaskProgress] = filteredResults
            return filteredResults
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    @MainActor
    var currentWeekProgress: [TaskProgress] {
        if let cached = cache[CacheKeys.currentWeekProgress] as? [TaskProgress] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
//            TODO do it on background
            let results = try modelContainer.mainContext.fetch(progressList)
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && isDateInCurrentWeek(progressItem.date)
            }
            cache[CacheKeys.currentWeekProgress] = filteredResults
            return filteredResults
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    @MainActor
    var yesterdayProgress: [TaskProgress] {
        if let cached = cache[CacheKeys.yesterdayProgress] as? [TaskProgress] {
            return cached
        }

        let fetch = FetchDescriptor<TaskProgress>()

        do {
            let results = try modelContainer.mainContext.fetch(fetch)

            let calendar = Calendar.current
            guard let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) else {
                return []
            }

            let filtered = results.filter { progressItem in
                guard progressItem.status == .finished else { return false }
                return calendar.isDate(progressItem.date, inSameDayAs: yesterday)
            }

            cache[CacheKeys.yesterdayProgress] = filtered
            return filtered
        } catch {
            print("Error fetching yesterday's progress: \(error)")
            return []
        }
    }

    @MainActor
    var todayTaskProgress: [TaskProgress] {
        if let cached = cache[CacheKeys.todayTaskProgress] as? [TaskProgress] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let calendar = Calendar.current
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && calendar.isDate(progressItem.date, inSameDayAs: .now)
            }
            cache[CacheKeys.todayTaskProgress] = filteredResults
            return filteredResults
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    @MainActor
    private var pieceListLast7Days: [String: [TaskProgress]] {
        return groupTaskProgressByPieceName(items: weekTaskProgress)
    }
    
    @MainActor
    var getLast7DaysPracticeList: [(String, Int)] {
        if let cached = cache[CacheKeys.last7DaysPracticeList] as? [(String, Int)] {
            return cached
        }
        
        let pieceList = pieceListLast7Days.sorted { $0.key > $1.key }
        let computedResult = pieceList.map { item in
            (item.key, getPracticeTime(of: item.value))
        }
        cache[CacheKeys.last7DaysPracticeList] = computedResult
        return computedResult
    }

    @MainActor
    var progressListByMonth: [Date: [TaskProgress]] {
        if let cached = cache[CacheKeys.progressListByMonth] as? [Date: [TaskProgress]] {
            return cached
        }

        let groupedResults = groupTaskProgressByMonth(items: filteredTaskProgress)
        cache[CacheKeys.progressListByMonth] = groupedResults
        return groupedResults
    }
    
    @MainActor
    var progressListByYear: [Date: [TaskProgress]] {
        if let cached = cache[CacheKeys.progressListByYear] as? [Date: [TaskProgress]] {
            return cached
        }

        let groupedResults = groupTaskProgressByYear(items: filteredTaskProgress)
        cache[CacheKeys.progressListByYear] = groupedResults
        return groupedResults
    }
    
    @MainActor
    var getLast7DaysPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.last7DaysPracticeTimeList] as? [(Date, Int)] {
            return cached
        }
        
        let days = getLast7DaysDateList()
        let list = days.map { day in
            (day, getPracticeTime(of: filteredTaskProgress, at: day))
        }
        cache[CacheKeys.last7DaysPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getLast14DaysPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.last14DaysPracticeTimeList] as? [(Date, Int)] {
            return cached
        }
        
        let days = getLast14DaysDateList()
        let list = days.map { day in
            (day, getPracticeTime(of: filteredTaskProgress, at: day))
        }
        cache[CacheKeys.last14DaysPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getLast30DaysPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.last30DaysPracticeTimeList] as? [(Date, Int)] {
            return cached
        }
        
        let days = getLast30DaysDateList()
        let list = days.map { day in
            (day, getPracticeTime(of: filteredTaskProgress, at: day))
        }
        cache[CacheKeys.last30DaysPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getLast100DaysPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.last100DaysPracticeTimeList] as? [(Date, Int)] {
            return cached
        }
        
        let days = getLast100DaysDateList()
        let list = days.map { day in
            (day, getPracticeTime(of: filteredTaskProgress, at: day))
        }
        cache[CacheKeys.last100DaysPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getLastHalfYearPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.lastHalfYearPracticeTimeList] as? [(Date, Int)] {
            return cached
        }

        let groupedByMonth = progressListByMonth
        let months = getLast6MonthsDateList()
        let list = months.map { month in
            (month, getPracticeTime(of: groupedByMonth[month] ?? []))
        }
        cache[CacheKeys.lastHalfYearPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getLast365DaysPracticeTimeList: [(Date, Int)] {
        if let cached = cache[CacheKeys.last365DaysPracticeTimeList] as? [(Date, Int)] {
            return cached
        }

        let groupedByMonth = progressListByMonth
        let months = getLast12MonthsDateList()
        let list = months.map { month in
            (month, getPracticeTime(of: groupedByMonth[month] ?? []))
        }
        cache[CacheKeys.last365DaysPracticeTimeList] = list
        return list
    }
    
    @MainActor
    var getTodayPracticeList: [(String, Int)] {
        if let cached = cache[CacheKeys.todayPracticeList] as? [(String, Int)] {
            return cached
        }

        let list = todayTaskProgress.map { item in
            return (item.task?.pieceName ?? "", item.practiceTime ?? 0)
        }
        cache[CacheKeys.todayPracticeList] = list
        return list
    }
    
    @MainActor
    func getPracticeList(of date: Date, by unit: TimeUnit) -> [(String, Int)] {
        let unitString = unit.rawValue
        let cacheKey = "\(CacheKeys.practiceListByUnit)_\(unitString)_\(formatDateToString(date))"
        if let cached = cache[cacheKey] as? [(String, Int)] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let calendar = Calendar.current
            
            // Calculate the start and end dates based on the time unit
            var startDate: Date
            var endDate: Date
            
            switch unit {
            case .day:
                // For day, we use the same day
                let components = calendar.dateComponents([.year, .month, .day], from: date)
                guard let dayStartDate = calendar.date(from: components) else { return [] }
                startDate = dayStartDate
                guard let dayEndDate = calendar.date(byAdding: .day, value: 1, to: dayStartDate) else { return [] }
                endDate = calendar.date(byAdding: .second, value: -1, to: dayEndDate) ?? dayEndDate
                
            case .week:
                // For week, we use 7 days starting from the given date
                startDate = date
                guard let weekEndDate = calendar.date(byAdding: .day, value: 6, to: startDate) else { return [] }
                endDate = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: weekEndDate) ?? weekEndDate
                
            case .month:
                // For month, we use the calendar month containing the given date
                let components = calendar.dateComponents([.year, .month], from: date)
                guard let monthStartDate = calendar.date(from: components),
                      let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStartDate) else { return [] }
                startDate = monthStartDate
                endDate = calendar.date(byAdding: .second, value: -1, to: nextMonth) ?? 
                          calendar.date(byAdding: .day, value: -1, to: nextMonth) ?? 
                          monthStartDate
                
            case .year:
                // For year, we use the calendar year containing the given date
                let components = calendar.dateComponents([.year], from: date)
                guard let yearStartDate = calendar.date(from: components),
                      let nextYear = calendar.date(byAdding: .year, value: 1, to: yearStartDate) else { return [] }
                startDate = yearStartDate
                endDate = calendar.date(byAdding: .second, value: -1, to: nextYear) ?? 
                          calendar.date(byAdding: .day, value: -1, to: nextYear) ?? 
                          yearStartDate
            }
            
            // Filter the results based on the date range
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && 
                       progressItem.date >= startDate &&
                       progressItem.date <= endDate
            }
            
            // Group by piece name and sum practice times
            var practiceByPiece: [String: Int] = [:]
            
            for item in filteredResults {
                let pieceName = item.task?.pieceName ?? "Unknown"
                let time = item.practiceTime ?? 0
                practiceByPiece[pieceName] = (practiceByPiece[pieceName] ?? 0) + time
            }
            
            let list = practiceByPiece.map { ($0.key, $0.value) }.sorted { $0.1 > $1.1 }
            cache[cacheKey] = list
            return list
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    // Legacy methods updated to use the unified method
    @MainActor
    func getPracticeListForDate(_ date: Date) -> [(String, Int)] {
        return getPracticeList(of: date, by: .day)
    }
    
    @MainActor
    func getPracticeListForWeek(_ weekStartDate: Date) -> [(String, Int)] {
        return getPracticeList(of: weekStartDate, by: .week)
    }
    
    @MainActor
    func getPracticeListForMonth(_ monthDate: Date) -> [(String, Int)] {
        return getPracticeList(of: monthDate, by: .month)
    }
    
    @MainActor
    func getPracticeListForYear(_ yearDate: Date) -> [(String, Int)] {
        return getPracticeList(of: yearDate, by: .year)
    }
    
    @MainActor
    func getDailyPracticeTimeForWeek(_ weekStartDate: Date) -> [(Date, Int)] {
        let cacheKey = "\(CacheKeys.dailyPracticeTimeForWeek)_\(formatDateToString(weekStartDate))"
        if let cached = cache[cacheKey] as? [(Date, Int)] {
            return cached
        }
        
        let calendar = Calendar.current
        let weekEndDate = calendar.date(byAdding: .day, value: 6, to: weekStartDate)!
        
        // 使用通用方法获取结果
        let dailyData = getDailyPracticeTimeForDateRange(from: weekStartDate, to: weekEndDate)
        cache[cacheKey] = dailyData
        return dailyData
    }
    
    @MainActor
    func getDailyPracticeTimeForMonth(_ monthDate: Date) -> [(Date, Int)] {
        let cacheKey = "\(CacheKeys.dailyPracticeTimeForMonth)_\(formatDateToString(monthDate))"
        if let cached = cache[cacheKey] as? [(Date, Int)] {
            return cached
        }
        
        let calendar = Calendar.current
        
        // 获取月份的起始日期和结束日期
        let components = calendar.dateComponents([.year, .month], from: monthDate)
        guard let monthStartDate = calendar.date(from: components),
              let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStartDate),
              let monthEndDate = calendar.date(byAdding: .day, value: -1, to: nextMonth) else {
            return []
        }
        
        // 使用通用方法获取结果
        let dailyData = getDailyPracticeTimeForDateRange(from: monthStartDate, to: monthEndDate)
        cache[cacheKey] = dailyData
        return dailyData
    }
    
    // 通用方法：获取指定日期范围内每天的练习时间
    @MainActor
    private func getDailyPracticeTimeForDateRange(from startDate: Date, to endDate: Date) -> [(Date, Int)] {
        let cacheKey = "\(CacheKeys.dailyPracticeTimeByRange)_\(formatDateToString(startDate))_\(formatDateToString(endDate))"
        if let cached = cache[cacheKey] as? [(Date, Int)] {
            return cached
        }
        
        let calendar = Calendar.current
        var dates: [Date] = []
        
        // 生成日期范围内的所有日期
        var currentDate = startDate
        while currentDate <= endDate {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }
        
        // 获取每天的练习数据
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished &&
                       progressItem.date >= calendar.startOfDay(for: startDate) &&
                       progressItem.date <= endOfDay(for: endDate)
            }
            
            // 按日期分组
            let dailyData = dates.map { date -> (Date, Int) in
                let dayStart = calendar.startOfDay(for: date)
                let dayEnd = endOfDay(for: date)
                
                let dayPractices = filteredResults.filter { progressItem in
                    return progressItem.date >= dayStart && progressItem.date <= dayEnd
                }
                
                return (date, getPracticeTime(of: dayPractices))
            }
            
            return dailyData
        } catch {
            print("Error fetching data for date range: \(error)")
            return dates.map { ($0, 0) }
        }
    }
    
    // 辅助方法：获取一天的结束时间
    private func endOfDay(for date: Date) -> Date {
        var components = Calendar.current.dateComponents([.year, .month, .day], from: date)
        components.hour = 23
        components.minute = 59
        components.second = 59
        return Calendar.current.date(from: components) ?? date
    }
    
    private func formatDateToString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    @MainActor
    func invalidateCache(forKey key: String? = nil) {
        if let key = key {
            cache.removeValue(forKey: key)
        } else {
            cache.removeAll()
        }
    }
    
    @MainActor
    var getMonthPracticeList: [(String, Int)] {
        if let cached = cache[CacheKeys.monthPracticeList] as? [(String, Int)] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let calendar = Calendar.current
            
            // Get the current month's start and end dates
            let now = Date()
            let components = calendar.dateComponents([.year, .month], from: now)
            guard let monthStartDate = calendar.date(from: components),
                  let monthEndDate = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: monthStartDate) else {
                return []
            }
            
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && 
                       progressItem.date >= monthStartDate &&
                       progressItem.date <= monthEndDate
            }
            
            // Group by piece name and sum practice times
            var practiceByPiece: [String: Int] = [:]
            
            for item in filteredResults {
                let pieceName = item.task?.pieceName ?? "Unknown"
                let time = item.practiceTime ?? 0
                practiceByPiece[pieceName] = (practiceByPiece[pieceName] ?? 0) + time
            }
            
            let list = practiceByPiece.map { ($0.key, $0.value) }.sorted { $0.1 > $1.1 }
            cache[CacheKeys.monthPracticeList] = list
            return list
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    @MainActor
    var getYearPracticeList: [(String, Int)] {
        if let cached = cache[CacheKeys.yearPracticeList] as? [(String, Int)] {
            return cached
        }
        
        let progressList = FetchDescriptor<TaskProgress>()
        
        do {
            let results = try modelContainer.mainContext.fetch(progressList)
            let calendar = Calendar.current
            
            // Get the current year's start and end dates
            let now = Date()
            let components = calendar.dateComponents([.year], from: now)
            guard let yearStartDate = calendar.date(from: components),
                  let yearEndDate = calendar.date(byAdding: DateComponents(year: 1, day: -1), to: yearStartDate) else {
                return []
            }
            
            let filteredResults = results.filter { progressItem in
                return progressItem.status == .finished && 
                       progressItem.date >= yearStartDate &&
                       progressItem.date <= yearEndDate
            }
            
            // Group by piece name and sum practice times
            var practiceByPiece: [String: Int] = [:]
            
            for item in filteredResults {
                let pieceName = item.task?.pieceName ?? "Unknown"
                let time = item.practiceTime ?? 0
                practiceByPiece[pieceName] = (practiceByPiece[pieceName] ?? 0) + time
            }
            
            let list = practiceByPiece.map { ($0.key, $0.value) }.sorted { $0.1 > $1.1 }
            cache[CacheKeys.yearPracticeList] = list
            return list
        } catch {
            print("Error fetching data: \(error)")
            return []
        }
    }
    
    @MainActor
    func getMonthlyPracticeTimeForYear(_ yearDate: Date) -> [(Date, Int)] {
        let cacheKey = "\(CacheKeys.monthlyPracticeTimeForYear)_\(formatDateToString(yearDate))"
        if let cached = cache[cacheKey] as? [(Date, Int)] {
            return cached
        }

        let calendar = Calendar.current
        let year = calendar.component(.year, from: yearDate)
        var monthDates: [Date] = []

        // Generate first day of each month for the year
        for month in 1...12 {
            if let date = calendar.date(from: DateComponents(year: year, month: month, day: 1)) {
                monthDates.append(date)
            }
        }

        // Get practice time for each month
        let monthlyData = monthDates.map { date -> (Date, Int) in
            let monthStart = date
            // Use the same date calculation logic as getPracticeList for consistency
            guard let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStart) else {
                return (date, 0)
            }
            let monthEnd = calendar.date(byAdding: .second, value: -1, to: nextMonth) ??
                          calendar.date(byAdding: .day, value: -1, to: nextMonth) ??
                          monthStart

            let progressList = FetchDescriptor<TaskProgress>()
            do {
                let results = try modelContainer.mainContext.fetch(progressList)
                let filteredResults = results.filter { progressItem in
                    return progressItem.status == .finished &&
                           progressItem.date >= monthStart &&
                           progressItem.date <= monthEnd
                }
                return (date, getPracticeTime(of: filteredResults))
            } catch {
                print("Error fetching data: \(error)")
                return (date, 0)
            }
        }

        cache[cacheKey] = monthlyData
        return monthlyData
    }
    
    @MainActor
    func getLongestPracticeStreak() -> Int {
        // TODO
        let dates = finishedPracticeDatesSorted()
        guard !dates.isEmpty else { return 0 }

        var longest = 1
        var currentStreak = 1

        for i in 1..<dates.count {
            let prev = dates[i - 1]
            let current = dates[i]
            if Calendar.current.isDate(current, inSameDayAs: Calendar.current.date(byAdding: .day, value: 1, to: prev)!) {
                currentStreak += 1
                longest = max(longest, currentStreak)
            } else if !Calendar.current.isDate(current, inSameDayAs: prev) {
                currentStreak = 1
            }
        }

        return longest
    }
    
    @MainActor
    func getCurrentPracticeStreak() -> Int {
        // TODO
        let datesSet = Set(finishedPracticeDatesSorted())
        guard !datesSet.isEmpty else { return 0 }
        
        var streak = 0
        var date = Calendar.current.startOfDay(for: Date())
        
        while datesSet.contains(date) {
            streak += 1
            guard let previousDay = Calendar.current.date(byAdding: .day, value: -1, to: date) else {
                break
            }
            date = previousDay
        }
        
        return streak
    }
    
    @MainActor
    private func finishedPracticeDatesSorted() -> [Date] {
        let finishedDates = filteredTaskProgress
            .filter { $0.status == .finished && ($0.practiceTime ?? 0) > 0 }
            .map { Calendar.current.startOfDay(for: $0.date) }

        return Array(Set(finishedDates)).sorted()
    }
}

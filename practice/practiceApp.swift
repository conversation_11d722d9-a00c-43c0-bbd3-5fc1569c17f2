//
//  practiceApp.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI
import Sentry

import SwiftData
import CloudKit
import FirebaseCore

// MARK: SwiftData Models
typealias TaskItem = TaskItemSchemaV1.TaskItem
typealias TaskProgress = TaskItemSchemaV1.TaskProgress
typealias RecordingItem = TaskItemSchemaV1.RecordingItem
typealias CollectionItem = TaskItemSchemaV1.CollectionItem
//typealias VideoItem = TaskItemSchemaV1.VideoItem

// MARK: Migration Plan
//enum TaskItemMigrationPlan: SchemaMigrationPlan {
//    static var schemas: [any VersionedSchema.Type] {
//        [TaskItemSchemaV1.self]
//    }
//    static var stages: [MigrationStage] {
//        []
//    }
//}

class AppDelegate: NSObject, UIApplicationDelegate {
  func application(_ application: UIApplication,
                   didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> <PERSON><PERSON> {
      SentrySDK.start { options in
          options.dsn = "https://<EMAIL>/4509293441122304"
//          options.debug = true // Enabled debug when first installing is always helpful
          
          // Adds IP for users.
          // For more information, visit: https://docs.sentry.io/platforms/apple/data-management/data-collected/
          options.sendDefaultPii = true
          
          // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
          // We recommend adjusting this value in production.
          options.tracesSampleRate = 1.0
          
          // Configure profiling. Visit https://docs.sentry.io/platforms/apple/profiling/ to learn more.
          options.configureProfiling = {
              $0.sessionSampleRate = 1.0 // We recommend adjusting this value in production.
              $0.lifecycle = .trace
          }
          
          // Uncomment the following lines to add more data to your events
           options.attachScreenshot = true // This adds a screenshot to the error events
           options.attachViewHierarchy = true // This adds the view hierarchy to the error events
      }
      // Remove the next line after confirming that your Sentry integration is working.
//      SentrySDK.capture(message: "This app uses Sentry! :)")
      FirebaseConfiguration.shared.setLoggerLevel(.min)
      FirebaseApp.configure()
      
      return true
  }
}


@main
struct practiceApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate

    var body: some Scene {
        WindowGroup {
            AppWrapper()
        }
        .modelContainer(TaskItemSchemaV1.container)
    }
}

struct AppWrapper: View {
  @Environment(\.modelContext) private var modelContext
  
  var body: some View {
    ContentView(modelContext: modelContext)
  }
}

//
//  DateUtils.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/2.
//

import Foundation

func getYearFrom(date: Date) -> Int {
    let calendar = Calendar.current // This gets the current user's calendar settings
    let year = calendar.component(.year, from: date)
    return year
}

enum TimeUnit {
    case minute
    case hour
}

func getTimeStringPair(of seconds: Int, unit: TimeUnit? = nil, decimal: Int = 1) -> (time: String, unit: String) {
    if let unit = unit {
        switch unit {
        case .minute:
            let minutes = seconds / 60
            let label = minutes == 1 ? String(localized: "minute") : String(localized: "minutes")
            return ("\(minutes)", label)
        case .hour:
            let hours = CGFloat(seconds) / 3600.0
            let label = abs(hours - 1.0) < 0.01 ? String(localized: "hour") : String(localized: "hours")
            return (trimmedDecimalString(from: hours, decimalPlaces: decimal), label)
        }
    } else {
        if seconds >= 3600 {
            let hours = CGFloat(seconds) / 3600.0
            let label = abs(hours - 1.0) < 0.01 ? String(localized: "hour") : String(localized: "hours")
            return (trimmedDecimalString(from: hours, decimalPlaces: decimal), label)
        } else {
            let minutes = seconds / 60
            let label = minutes == 1 ? String(localized: "minute") : String(localized: "minutes")
            return ("\(minutes)", label)
        }
    }
}

func getTimeString(of seconds: Int, unit: TimeUnit? = nil, decimal: Int = 1) -> String {
    if let unit = unit {
        switch unit {
        case .minute:
            let minutes = seconds / 60
            let label = minutes == 1 ? String(localized: "minute") : String(localized: "minutes")
            return "\(minutes) \(label)"
        case .hour:
            let hours =  CGFloat(seconds) / 3600.0
            let label = (abs(hours - 1.0) < 0.01) ? String(localized: "hour") : String(localized: "hours")
            return "\(trimmedDecimalString(from: hours, decimalPlaces: decimal)) \(label)"
        }
    } else {
        if seconds >= 3600 {
            let hours = CGFloat(seconds) / 3600.0
            let label = (abs(hours - 1.0) < 0.01) ? String(localized: "hour") : String(localized: "hours")
            return "\(trimmedDecimalString(from: hours, decimalPlaces: decimal)) \(label)"
        } else {
            let minutes = seconds / 60
            let label = minutes == 1 ? String(localized: "minute") : String(localized: "minutes")
            return "\(minutes) \(label)"
        }
    }
}

func firstDateOfMonth(of date: Date) -> Date {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year, .month], from: date)
    let startOfMonth = calendar.date(from: components)!
    
    return startOfMonth
}

func getStandarDateString (of date: Date) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd"
    let dateString = dateFormatter.string(from: date)
    return dateString
}

func groupTaskProgressByMonth(items: [TaskProgress]) -> [Date: [TaskProgress]] {
    var groupedItems: [Date: [TaskProgress]] = [:]
    
    for item in items {
        let monthStart = firstDateOfMonth(of: item.date)
        
        if groupedItems[monthStart] != nil {
            groupedItems[monthStart]!.append(item)
        } else {
            groupedItems[monthStart] = [item]
        }
    }
    
    return groupedItems
}

func groupTaskProgressByPieceName(items: [TaskProgress]) -> [String: [TaskProgress]] {
    var groupedItems: [String: [TaskProgress]] = [:]
    
    for item in items {
        let pieceName = item.task?.pieceName
        if let pieceName = pieceName {
            
            if groupedItems[pieceName] != nil {
                groupedItems[pieceName]!.append(item)
            } else {
                groupedItems[pieceName] = [item]
            }
        }
    }
    
    return groupedItems
}

func firstDateOfYear(of date: Date) -> Date {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year], from: date)
    let startOfYear = calendar.date(from: components)!
    
    return startOfYear
}

func firstDateOfYear(of year: Int) -> Date {
    var components = DateComponents()
    components.year = year
    components.month = 1 // January
    components.day = 1 // First day of the month
    let calendar = Calendar.current
    return calendar.date(from: components) ?? .now
}

func getStringFrom(date: Date) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
    dateFormatter.locale = Locale(identifier: "en_US_POSIX")
    
    return dateFormatter.string(from: date)
}

func getDateFrom(string: String?) -> Date? {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
    dateFormatter.locale = Locale(identifier: "en_US_POSIX")
    
    if let string = string, let date = dateFormatter.date(from: string) {
        return date
    } else {
        return .now
    }
}


func groupTaskProgressByYear(items: [TaskProgress]) -> [Date: [TaskProgress]] {
    var groupedItems: [Date: [TaskProgress]] = [:]
    
    for item in items {
        let yearStart = firstDateOfYear(of: item.date)
        
        if groupedItems[yearStart] != nil {
            groupedItems[yearStart]!.append(item)
        } else {
            groupedItems[yearStart] = [item]
        }
    }
    
    return groupedItems
}


// get day number in month from date
func dayOfMonth(from date: Date) -> Int {
    return Calendar.current.component(.day, from: date)
}

// get month abbr from date
func getMonthAbbr(from date: Date) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.locale = Locale(identifier: "en_US")
    dateFormatter.dateFormat = "MMM"
    let monthAbbr = dateFormatter.string(from: date)
    return monthAbbr
}

func getMonthAbbrList () -> [String] {
    let dateFormatter = DateFormatter()
    dateFormatter.locale = Locale(identifier: "en_US")
    return dateFormatter.shortMonthSymbols
}

func getDayDifference(from startDate: Date, to endDate: Date) -> Int {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.day], from: startDate, to: endDate)
    return (components.day ?? 0) + 1
}

func getLastNDaysList(dayNum: Int) -> [Date] {
    var dates: [Date] = []
    let calendar = Calendar.current
    let today = Date()

    for daysAgo in (0..<dayNum).reversed() {
        if let date = calendar.date(byAdding: .day, value: -daysAgo, to: today) {
            dates.append(calendar.startOfDay(for: date))
        }
    }

    return dates
}

var shortTimeFormatter: DateFormatter {
    let formatter = DateFormatter()
    formatter.timeStyle = .short
    return formatter
}

func getLastNMonthsList(monthNum: Int) -> [Date] {
    var dates: [Date] = []
    let calendar = Calendar.current
    let today = Date()

    for monthsAgo in (0..<monthNum).reversed() {
        if let date = calendar.date(byAdding: .month, value: -monthsAgo, to: today),
           let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: date)) {
            dates.append(startOfMonth)
        }
    }
    return dates
}

func isDateInLastWeek(_ date: Date) -> Bool {
    let calendar = Calendar.current
    let now = Date()
    guard let oneWeekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) else {
        return false
    }

    return date >= oneWeekAgo && date <= now
}

func isDateInCurrentWeek(_ date: Date) -> Bool {
    var calendar = Calendar.current
    calendar.firstWeekday = 1
    let now = Date()

    guard let weekInterval = calendar.dateInterval(of: .weekOfYear, for: now) else {
        return false
    }

    return weekInterval.contains(date)
}

func getLast7DaysDateList() -> [Date] {
    getLastNDaysList(dayNum: 7)
}

func getLast14DaysDateList() -> [Date] {
    getLastNDaysList(dayNum: 14)
}

func getLast30DaysDateList() -> [Date] {
    getLastNDaysList(dayNum: 30)
}

func getLast100DaysDateList() -> [Date] {
    getLastNDaysList(dayNum: 100)
}

func getLast365DaysDateList() -> [Date] {
    getLastNDaysList(dayNum: 365)
}

func getLast6MonthsDateList() -> [Date] {
    getLastNMonthsList(monthNum: 6)
}

func getLast12MonthsDateList() -> [Date] {
    getLastNMonthsList(monthNum: 12)
}

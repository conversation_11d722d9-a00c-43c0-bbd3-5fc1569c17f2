//
//  ColorUnit.swift
//  practice
//
//  Created by 陈昇 on 3/13/24.
//

import Foundation
import SwiftUI

func calcCardCellColorFrom(seconds: Int, targetTime: Int? = 120) -> Color {
    let targetTime = (targetTime != nil) ? Double(targetTime!) : 120
    switch seconds {
    case 0:
        return .gray.opacity(0.1)
    case 1 ..< Int(0.2 * targetTime):
        return .green.opacity(0.2)
    case Int(targetTime * 0.2) ..< Int(targetTime * 0.4):
        return .green.opacity(0.4)
    case Int(targetTime * 0.4) ..< Int(targetTime * 0.6):
        return .green.opacity(0.6)
    case Int(targetTime * 0.6) ..< Int(targetTime * 0.8):
        return .green.opacity(0.8)
    case Int(targetTime * 0.8)...:
        return .green
    default:
        return .gray.opacity(0.2)
    }
}


// helper func to mock data
func randomColor() -> Color {
    let segments: [Color] = [.gray.opacity(0.2), .green.opacity(0.2), .green.opacity(0.4), .green.opacity(0.6),
        .green.opacity(0.8),
        .green
    ]
    guard let selectedSegment = segments.randomElement() else {
        return .gray.opacity(0.2)
    }
    return selectedSegment
}

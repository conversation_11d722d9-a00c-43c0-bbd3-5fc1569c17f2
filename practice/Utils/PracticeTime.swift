//
//  PracticeTime.swift
//  practice
//
//  Created by 陈昇 on 3/13/24.
//

import Foundation

func getPracticeTime(of task: TaskItem) -> Int {
    var totalSeconds = 0
    for progress in task.taskProgress ?? [] {
        if let practiceTime = progress.practiceTime {
            totalSeconds += practiceTime
        }
    }
    return totalSeconds
}

func getPracticeTime(of taskProgress: [TaskProgress]) -> Int {
    var totalSeconds = 0
    for progress in taskProgress {
        if let practiceTime = progress.practiceTime {
            totalSeconds += practiceTime
        }
    }
    return totalSeconds
}

func getPracticeTime(of taskProgress: [TaskProgress], at date: Date) -> Int {
    var totalSeconds = 0
    let calendar = Calendar.current
    let startOfDay = calendar.startOfDay(for: date)
    
    for progress in taskProgress {
        if calendar.isDate(progress.date, inSameDayAs: startOfDay) {
            if let practiceTime = progress.practiceTime {
                totalSeconds += practiceTime
            }
        }
    }
    
    return totalSeconds
}

func getPracticeTime(of taskList: [TaskItem]) -> Int {
    var totalSeconds = 0
    for task in taskList {
        totalSeconds += getPracticeTime(of: task)
    }
    return totalSeconds
}

func getPracticeTime(of taskList: [TaskItem], at date: Date) -> Int {
    var totalSeconds = 0
    for task in taskList {
        totalSeconds += getPracticeTime(of: task, at: date)
    }
    return totalSeconds
}


func getPracticeTime(of task: TaskItem, at date: Date) -> Int {
    let startOfDay = Calendar.current.startOfDay(for: date)
    return (task.taskProgress ?? []).first { progress in
        return Calendar.current.isDate(progress.date, inSameDayAs: startOfDay)
    }?.practiceTime ?? 0
}

func getPracticeTime(of task: TaskItem, in dateRange: (start: Date, end: Date)) -> Int {
    let totalPracticeTime = (task.taskProgress ?? []).reduce(0) { (total, progress) -> Int in
        // Ensure the date of this progress is within the given date range (inclusive)
        if progress.date >= dateRange.start && progress.date <= dateRange.end {
            return total + (progress.practiceTime ?? 0)
        }
        return total
    }
    return totalPracticeTime
}

func getPracticeTime(of taskList: [TaskItem], in dateRange: (start: Date, end: Date)) -> Int {
    var totalSeconds = 0
    for task in taskList {
        totalSeconds += getPracticeTime(of: task, in: (dateRange.start, dateRange.end))
    }
    return totalSeconds
}

func getPracticeTimeDateMap(of task: TaskItem) -> [Date: TaskProgress] {
    let calendar = Calendar.current
    var timeMap: [Date: TaskProgress] = [:]
    for progress in task.taskProgress ?? [] {
        timeMap[calendar.startOfDay(for: progress.date)] = progress
    }
    return timeMap
}


func getPracticeDays(of taskProgress: [TaskProgress]) -> Int {
    var totalDays = 0
    var dateSet: Set<Date> = []
    let calendar = Calendar.current
    for progress in taskProgress {
        if let practiceTime = progress.practiceTime {
            if !dateSet.contains(calendar.startOfDay(for: progress.date)) {
                dateSet.insert(calendar.startOfDay(for: progress.date))
                if practiceTime > 0 {
                    totalDays += 1
                }
            }
        }
    }
    return totalDays
}

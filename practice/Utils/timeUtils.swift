//
//  timeUtils.swift
//  practice
//
//  Created by 陈昇 on 4/2/24.
//

import Foundation

func timerString(from seconds: Int, showHour: Bool = false) -> String {
    let formatter = DateComponentsFormatter()
    formatter.allowedUnits = (seconds >= 3600 && showHour) ? [.hour, .minute, .second] : [.minute, .second]
    formatter.unitsStyle = .positional
    formatter.zeroFormattingBehavior = .pad
    
    return formatter.string(from: TimeInterval(seconds)) ?? "00:00"
}

func timerString(from remainingTime: ClosedRange<Date>) -> String {
    let formatter = DateComponentsFormatter()
    formatter.allowedUnits = [.hour, .minute, .second]
    formatter.unitsStyle = .positional
    formatter.zeroFormattingBehavior = .pad
    
    let currentDate = Date()
    let remainingSeconds = remainingTime.upperBound.timeIntervalSince(currentDate)
    
    return formatter.string(from: remainingSeconds) ?? "00:00"
}

func timeString(from time: TimeInterval) -> String {
    let minutes = Int(time) / 60
    let seconds = Int(time) % 60
    return String(format: "%02d:%02d", minutes, seconds)
}

// 省略小数点后面的 0
func trimmedDecimalString(from value: Double, decimalPlaces: Int = 1) -> String {
    let roundedNumber = Double(String(format: "%.\(decimalPlaces)f", value)) ?? 0
    if abs(roundedNumber.truncatingRemainder(dividingBy: 1)) < (decimalPlaces == 0 ? 0 : pow(0.1, Double(decimalPlaces))) {
        return String(format: "%.0f", value)
    } else {
        return String(format: "%.\(decimalPlaces)f", value)
    }
}

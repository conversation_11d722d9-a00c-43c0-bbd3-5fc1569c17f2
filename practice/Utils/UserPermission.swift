//
//  UserPermission.swift
//  practice
//
//  Created by 陈昇 on 3/11/24.
//

import Foundation
import UserNotifications

func getNotificationPermission () {
    UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { success, error in
        if success {
            print("Permission granted")
        } else if let error = error {
            print("Permission denied: \(error)")
        }
    }
}

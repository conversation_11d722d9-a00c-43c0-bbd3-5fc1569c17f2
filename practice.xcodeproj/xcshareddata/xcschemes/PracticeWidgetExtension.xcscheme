<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1620"
   wasCreatedForAppExtension = "YES"
   version = "2.2">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <AutocreatedTestPlanReference>
            </AutocreatedTestPlanReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A9F844002B842C8D00D35B12"
               BuildableName = "PracticeWidgetExtension.appex"
               BlueprintName = "PracticeWidgetExtension"
               ReferencedContainer = "container:practice.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "A98C06CB2B651BB9005AC0F3"
               BuildableName = "practice.app"
               BlueprintName = "practice"
               ReferencedContainer = "container:practice.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      launchStyle = "0"
      askForAppToLaunch = "Yes"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES"
      launchAutomaticallySubstyle = "2">
      <RemoteRunnable
         runnableDebuggingMode = "2"
         BundleIdentifier = "com.apple.springboard">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "A9F844002B842C8D00D35B12"
            BuildableName = "PracticeWidgetExtension.appex"
            BlueprintName = "PracticeWidgetExtension"
            ReferencedContainer = "container:practice.xcodeproj">
         </BuildableReference>
      </RemoteRunnable>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "A98C06CB2B651BB9005AC0F3"
            BuildableName = "practice.app"
            BlueprintName = "practice"
            ReferencedContainer = "container:practice.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "_XCWidgetKind"
            value = ""
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetDefaultView"
            value = "timeline"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "_XCWidgetFamily"
            value = "systemMedium"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES"
      askForAppToLaunch = "Yes"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "A98C06CB2B651BB9005AC0F3"
            BuildableName = "practice.app"
            BlueprintName = "practice"
            ReferencedContainer = "container:practice.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>

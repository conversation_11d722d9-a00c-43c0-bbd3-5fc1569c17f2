// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		54F20DBE172F44D89E8F0A0B /* Sentry in Frameworks */ = {isa = PBXBuildFile; productRef = EA2A596CC2394E5BAB60C153 /* Sentry */; };
		570D6F822BA4284700BA0C0A /* HorizonCalendar in Frameworks */ = {isa = PBXBuildFile; productRef = 570D6F812BA4284700BA0C0A /* HorizonCalendar */; };
		5731344E2BCE5DC800A2DFCE /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 5761B5082BCE32C300E1D5A1 /* Localizable.xcstrings */; };
		5731344F2BCE5DC900A2DFCE /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 5761B5082BCE32C300E1D5A1 /* Localizable.xcstrings */; };
		573F78872BFB2CF8004D7EBC /* StatisticsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 573F78862BFB2CF8004D7EBC /* StatisticsView.swift */; };
		5751A5E62BAAE637004932E4 /* CalendarViewUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5751A5E52BAAE637004932E4 /* CalendarViewUI.swift */; };
		5754638F2BB17F6800F8837A /* InfoCardGalleryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5754638E2BB17F6800F8837A /* InfoCardGalleryView.swift */; };
		575BDBC62D23C7460021C27C /* SubscribeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 575BDBC52D23C7460021C27C /* SubscribeView.swift */; };
		575BDBC82D23CD520021C27C /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 575BDBC72D23CD520021C27C /* StoreKit.framework */; };
		575BDBCA2D23CE3E0021C27C /* Product.storekit in Resources */ = {isa = PBXBuildFile; fileRef = 575BDBC92D23CE3E0021C27C /* Product.storekit */; };
		5761B5092BCE32C300E1D5A1 /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 5761B5082BCE32C300E1D5A1 /* Localizable.xcstrings */; };
		576A9D142BB54F6C002A5949 /* CountdownTimerLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D132BB54F6B002A5949 /* CountdownTimerLiveActivity.swift */; };
		576A9D1D2BBA8151002A5949 /* CountDownTimePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D1C2BBA8150002A5949 /* CountDownTimePicker.swift */; };
		576A9D202BBBA203002A5949 /* CountingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D1F2BBBA203002A5949 /* CountingView.swift */; };
		576A9D232BBBA6BF002A5949 /* CountDownContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D222BBBA6BF002A5949 /* CountDownContainer.swift */; };
		576A9D252BBC1B43002A5949 /* timeUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D242BBC1B43002A5949 /* timeUtils.swift */; };
		576B36DF2C0DBC1B00462961 /* DailySummaryWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576B36DD2C0DB1C700462961 /* DailySummaryWidget.swift */; };
		576B36E02C0DBC3500462961 /* DailySummaryWidgetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576B36DB2C0DB0BC00462961 /* DailySummaryWidgetView.swift */; };
		576B36E22C0DC4BE00462961 /* TaskProgressData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57793C472C06D560007D80A8 /* TaskProgressData.swift */; };
		57793C462C06D54E007D80A8 /* TaskItemData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57793C452C06D54E007D80A8 /* TaskItemData.swift */; };
		57793C482C06D560007D80A8 /* TaskProgressData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57793C472C06D560007D80A8 /* TaskProgressData.swift */; };
		577F78F82B9EFF59006C34CE /* UserPermission.swift in Sources */ = {isa = PBXBuildFile; fileRef = 577F78F72B9EFF59006C34CE /* UserPermission.swift */; };
		578197F92BB15EC10045F6C2 /* InfoCardCompactView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 578197F82BB15EC10045F6C2 /* InfoCardCompactView.swift */; };
		578920F42BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */; };
		578920F52BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */; };
		578920F62BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */; };
		578920F72BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */; };
		57980D2B2BBD12320048CB64 /* CountupTimerLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57980D2A2BBD12320048CB64 /* CountupTimerLiveActivity.swift */; };
		57980D2D2BBD33EE0048CB64 /* timeUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D242BBC1B43002A5949 /* timeUtils.swift */; };
		57B005322BA153150095DEAC /* PracticeTime.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B005312BA153150095DEAC /* PracticeTime.swift */; };
		57B005352BA19DCB0095DEAC /* ColorUnit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B005342BA19DCB0095DEAC /* ColorUnit.swift */; };
		57B005362BA1A0350095DEAC /* ColorUnit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B005342BA19DCB0095DEAC /* ColorUnit.swift */; };
		57B005372BA1AEBD0095DEAC /* DateUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = A92CF0D32B6D352700D48AC2 /* DateUtils.swift */; };
		57B005382BA2E7980095DEAC /* PracticeTime.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B005312BA153150095DEAC /* PracticeTime.swift */; };
		57B0053A2BA2F2610095DEAC /* PracticeTimeEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B005392BA2F2610095DEAC /* PracticeTimeEditView.swift */; };
		57B4DE282BD10EBF000ABCC7 /* QRCode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57B4DE272BD10EBF000ABCC7 /* QRCode.swift */; };
		57C939FA2BD2620900C391AB /* ShareImageFooter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57C939F92BD2620900C391AB /* ShareImageFooter.swift */; };
		57D41A6F2BC8DDF600EDBDA0 /* ArcProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57D41A6E2BC8DDF600EDBDA0 /* ArcProgressView.swift */; };
		57D41A722BC8FCAF00EDBDA0 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57D41A712BC8FCAF00EDBDA0 /* SettingsView.swift */; };
		57D41A742BC90A8D00EDBDA0 /* PersonalConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57D41A732BC90A8D00EDBDA0 /* PersonalConfig.swift */; };
		57D41A752BC90CA200EDBDA0 /* PersonalConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57D41A732BC90A8D00EDBDA0 /* PersonalConfig.swift */; };
		57E21D732BDB4FD300CC6072 /* ConfettiSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 57E21D722BDB4FD300CC6072 /* ConfettiSwiftUI */; };
		57E7CA1C2BA425CD001BD416 /* TimeModifyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57E7CA1B2BA425CD001BD416 /* TimeModifyView.swift */; };
		57F473EC2BC63579007E3315 /* PracticeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57F473EB2BC63579007E3315 /* PracticeView.swift */; };
		57F473F22BC697CD007E3315 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A9F844032B842C8D00D35B12 /* WidgetKit.framework */; };
		57F473F32BC697CD007E3315 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A9F844052B842C8D00D35B12 /* SwiftUI.framework */; };
		57F473F62BC697CD007E3315 /* PracticeLiveActivityWidgetBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57F473F52BC697CD007E3315 /* PracticeLiveActivityWidgetBundle.swift */; };
		57F473F82BC697CD007E3315 /* PracticeLiveActivityWidgetLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57F473F72BC697CD007E3315 /* PracticeLiveActivityWidgetLiveActivity.swift */; };
		57F473FA2BC697CD007E3315 /* PracticeLiveActivityWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57F473F92BC697CD007E3315 /* PracticeLiveActivityWidget.swift */; };
		57F473FC2BC697CD007E3315 /* AppIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57F473FB2BC697CD007E3315 /* AppIntent.swift */; };
		57F473FE2BC697CF007E3315 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 57F473FD2BC697CF007E3315 /* Assets.xcassets */; };
		57F474022BC697CF007E3315 /* PracticeLiveActivityWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 57F473F12BC697CD007E3315 /* PracticeLiveActivityWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		57F474062BC697EF007E3315 /* PracticeWidgetLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8440A2B842C8D00D35B12 /* PracticeWidgetLiveActivity.swift */; };
		57F474072BC69838007E3315 /* CountdownTimerLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 576A9D132BB54F6B002A5949 /* CountdownTimerLiveActivity.swift */; };
		57F474082BC6A4AB007E3315 /* CountupTimerLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57980D2A2BBD12320048CB64 /* CountupTimerLiveActivity.swift */; };
		A92CF0CE2B6BC47000D48AC2 /* ProgressCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A92CF0CD2B6BC46F00D48AC2 /* ProgressCardView.swift */; };
		A92CF0D12B6BDC2900D48AC2 /* TaskDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A92CF0D02B6BDC2900D48AC2 /* TaskDetailView.swift */; };
		A92CF0D42B6D352700D48AC2 /* DateUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = A92CF0D32B6D352700D48AC2 /* DateUtils.swift */; };
		A92CF0D62B6FD6CF00D48AC2 /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A92CF0D52B6FD6CF00D48AC2 /* Launch Screen.storyboard */; };
		A93BF7B62BC11E230074FA2B /* InfoCardTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A93BF7B52BC11E230074FA2B /* InfoCardTimeView.swift */; };
		A96344832B67E1B50058F857 /* TaskItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = A96344822B67E1B50058F857 /* TaskItem.swift */; };
		A97FFB272B72834500AED8F8 /* AddNewAchievementView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A97FFB262B72834500AED8F8 /* AddNewAchievementView.swift */; };
		A98C06D02B651BB9005AC0F3 /* practiceApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C06CF2B651BB9005AC0F3 /* practiceApp.swift */; };
		A98C06D22B651BB9005AC0F3 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C06D12B651BB9005AC0F3 /* ContentView.swift */; };
		A98C06D62B651BBC005AC0F3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A98C06D52B651BBC005AC0F3 /* Assets.xcassets */; };
		A98C06D92B651BBC005AC0F3 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A98C06D82B651BBC005AC0F3 /* Preview Assets.xcassets */; };
		A98C07012B652232005AC0F3 /* CurrentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C07002B652232005AC0F3 /* CurrentListView.swift */; };
		A98C07072B652E45005AC0F3 /* AddNewItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C07062B652E45005AC0F3 /* AddNewItemView.swift */; };
		A98C070D2B653A7B005AC0F3 /* AchievementListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C070C2B653A7B005AC0F3 /* AchievementListView.swift */; };
		A98C07102B654321005AC0F3 /* InfoCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A98C070F2B654321005AC0F3 /* InfoCardView.swift */; };
		A9D74C1C2B97702C00A92E3C /* TimerScrollSelect.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9D74C1B2B97702C00A92E3C /* TimerScrollSelect.swift */; };
		A9F843FC2B8384F900D35B12 /* ProgressYearCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F843FB2B8384F900D35B12 /* ProgressYearCard.swift */; };
		A9F844042B842C8D00D35B12 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A9F844032B842C8D00D35B12 /* WidgetKit.framework */; };
		A9F844062B842C8D00D35B12 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A9F844052B842C8D00D35B12 /* SwiftUI.framework */; };
		A9F844092B842C8D00D35B12 /* PracticeWidgetBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F844082B842C8D00D35B12 /* PracticeWidgetBundle.swift */; };
		A9F8440D2B842C8D00D35B12 /* PracticeWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8440C2B842C8D00D35B12 /* PracticeWidget.swift */; };
		A9F8440F2B842C8D00D35B12 /* YearProcessWidgetIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8440E2B842C8D00D35B12 /* YearProcessWidgetIntent.swift */; };
		A9F844112B842C8F00D35B12 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A9F844102B842C8F00D35B12 /* Assets.xcassets */; };
		A9F844152B842C8F00D35B12 /* PracticeWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = A9F844012B842C8D00D35B12 /* PracticeWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		A9F8442A2B84D51900D35B12 /* TaskItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = A96344822B67E1B50058F857 /* TaskItem.swift */; };
		A9F8442B2B84D59C00D35B12 /* ProgressYearCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F843FB2B8384F900D35B12 /* ProgressYearCard.swift */; };
		A9F8442E2B84D95900D35B12 /* YearProcessWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8442C2B84D8CD00D35B12 /* YearProcessWidget.swift */; };
		A9F8444E2B8E12DB00D35B12 /* CountDownView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8444D2B8E12DB00D35B12 /* CountDownView.swift */; };
		A9F844502B8E315100D35B12 /* MonthCalendarProgressCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9F8444F2B8E315100D35B12 /* MonthCalendarProgressCardView.swift */; };
		C105312D2DE89FB200B3A029 /* AppGlobalStateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C105312C2DE89FB200B3A029 /* AppGlobalStateManager.swift */; };
		C10DEDBA2DF9B6D6007828F5 /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = C10DEDB92DF9B6CF007828F5 /* readme.md */; };
		C10DEDBB2DF9B6D6007828F5 /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = C10DEDB92DF9B6CF007828F5 /* readme.md */; };
		C10DEDBC2DF9B6D6007828F5 /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = C10DEDB92DF9B6CF007828F5 /* readme.md */; };
		C10DEDBD2DF9B6D6007828F5 /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = C10DEDB92DF9B6CF007828F5 /* readme.md */; };
		C10DEDBF2DF9C3BB007828F5 /* CollectionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDBE2DF9C3BB007828F5 /* CollectionManager.swift */; };
		C10DEDC42DF9C3E0007828F5 /* CollectionListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDC12DF9C3E0007828F5 /* CollectionListView.swift */; };
		C10DEDC82DF9C8B2007828F5 /* CreateCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDC72DF9C8B2007828F5 /* CreateCollectionView.swift */; };
		C10DEDC92DF9C8B2007828F5 /* CollectionDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDC62DF9C8B2007828F5 /* CollectionDetailView.swift */; };
		C10DEDCB2DFA5719007828F5 /* CollectionItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDCA2DFA5719007828F5 /* CollectionItemView.swift */; };
		C10DEDCD2DFA58D4007828F5 /* TaskCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDCC2DFA58D4007828F5 /* TaskCardView.swift */; };
		C10DEDD02DFA7581007828F5 /* CollectionSortView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDCE2DFA7581007828F5 /* CollectionSortView.swift */; };
		C10DEDD42DFAA82A007828F5 /* ScrollableCoverView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD32DFAA82A007828F5 /* ScrollableCoverView.swift */; };
		C10DEDD62DFAB4D0007828F5 /* ListCoverView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD52DFAB4D0007828F5 /* ListCoverView.swift */; };
		C10DEDD82DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */; };
		C10DEDD92DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */; };
		C10DEDDA2DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */; };
		C10DEDDB2DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */ = {isa = PBXBuildFile; fileRef = C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */; };
		C114529E2DAEA21D00E54806 /* RatingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C114529D2DAEA21900E54806 /* RatingManager.swift */; };
		C11452A02DAF637F00E54806 /* RecordingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C114529F2DAF637A00E54806 /* RecordingManager.swift */; };
		C11452A62DAFA90C00E54806 /* RecordingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11452A52DAFA90C00E54806 /* RecordingView.swift */; };
		C11452A82DAFB12300E54806 /* Waveform.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11452A72DAFB12300E54806 /* Waveform.swift */; };
		C11452AF2DB0EB5E00E54806 /* PlaybackSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11452AE2DB0EB5E00E54806 /* PlaybackSliderView.swift */; };
		C11452B32DB105E200E54806 /* RecordingItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11452B02DB105E200E54806 /* RecordingItemView.swift */; };
		C11537D32DDD891300897938 /* CustomFontSize.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11537D22DDD891300897938 /* CustomFontSize.swift */; };
		C11976582DB10C000079670B /* LaurelTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976572DB10C000079670B /* LaurelTimeView.swift */; };
		C11976592DB10C000079670B /* LaurelTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976572DB10C000079670B /* LaurelTimeView.swift */; };
		C119765A2DB10C000079670B /* LaurelTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976572DB10C000079670B /* LaurelTimeView.swift */; };
		C119765B2DB10C000079670B /* LaurelTimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976572DB10C000079670B /* LaurelTimeView.swift */; };
		C119765F2DB165A30079670B /* CustomAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C119765C2DB165A30079670B /* CustomAlertView.swift */; };
		C11976652DB384FC0079670B /* TaskStatisticsNavigator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976612DB384FC0079670B /* TaskStatisticsNavigator.swift */; };
		C11976672DB50DF20079670B /* NavigationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976662DB50DF20079670B /* NavigationManager.swift */; };
		C11976692DB524DE0079670B /* ActionSheetWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = C11976682DB524DE0079670B /* ActionSheetWrapper.swift */; };
		C119766B2DB5482C0079670B /* TaskManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C119766A2DB5482C0079670B /* TaskManager.swift */; };
		C119766D2DB63BA50079670B /* DaySummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C119766C2DB63BA50079670B /* DaySummaryView.swift */; };
		C119766F2DB63BF10079670B /* WeekSummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C119766E2DB63BF10079670B /* WeekSummaryView.swift */; };
		C122C0492DD987B60004E0A7 /* ChildShape.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C0482DD987B60004E0A7 /* ChildShape.swift */; };
		C122C04A2DD987B60004E0A7 /* ChildShape.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C0482DD987B60004E0A7 /* ChildShape.swift */; };
		C122C04B2DD987B60004E0A7 /* ChildShape.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C0482DD987B60004E0A7 /* ChildShape.swift */; };
		C122C04C2DD987B60004E0A7 /* ChildShape.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C0482DD987B60004E0A7 /* ChildShape.swift */; };
		C122C04E2DDAC34B0004E0A7 /* StreakCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C04D2DDAC34B0004E0A7 /* StreakCardView.swift */; };
		C122C0552DDACF110004E0A7 /* Nunito-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C122C0502DDACF110004E0A7 /* Nunito-Black.ttf */; };
		C122C0562DDACF110004E0A7 /* Nunito-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C122C0522DDACF110004E0A7 /* Nunito-Medium.ttf */; };
		C122C0572DDACF110004E0A7 /* Nunito-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C122C0532DDACF110004E0A7 /* Nunito-Regular.ttf */; };
		C122C0582DDACF110004E0A7 /* Nunito-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C122C0542DDACF110004E0A7 /* Nunito-SemiBold.ttf */; };
		C122C0592DDACF110004E0A7 /* Nunito-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C122C0512DDACF110004E0A7 /* Nunito-Bold.ttf */; };
		C122C05B2DDAE8080004E0A7 /* TodayProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C122C05A2DDAE8080004E0A7 /* TodayProgressView.swift */; };
		C1256BD22DDB694B006DC118 /* TaskInfoCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BD12DDB694B006DC118 /* TaskInfoCard.swift */; };
		C1256BD42DDB731A006DC118 /* NotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BD32DDB731A006DC118 /* NotificationManager.swift */; };
		C1256BD62DDB8071006DC118 /* StepGuider.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BD52DDB8071006DC118 /* StepGuider.swift */; };
		C1256BD82DDB827D006DC118 /* OnboardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BD72DDB827D006DC118 /* OnboardingView.swift */; };
		C1256BDA2DDB8668006DC118 /* ReminderTimeSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BD92DDB8668006DC118 /* ReminderTimeSelector.swift */; };
		C1256BDC2DDBB84C006DC118 /* CustomSubscribeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BDB2DDBB84C006DC118 /* CustomSubscribeView.swift */; };
		C1256BDE2DDC4989006DC118 /* AnimatableNumber.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BDD2DDC4989006DC118 /* AnimatableNumber.swift */; };
		C1256BE12DDC7148006DC118 /* HapticManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BE02DDC7148006DC118 /* HapticManager.swift */; };
		C1256BE32DDD6F8A006DC118 /* ViewExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1256BE22DDD6F8A006DC118 /* ViewExtension.swift */; };
		C142722A2DCE3B9300EBB0B6 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = C14272292DCE3B9300EBB0B6 /* FirebaseCrashlytics */; };
		C142722D2DCE3F1800EBB0B6 /* FirebasePerformance in Frameworks */ = {isa = PBXBuildFile; productRef = C142722C2DCE3F1800EBB0B6 /* FirebasePerformance */; };
		C14527B52DE5FFB20026ACA8 /* MatchGeo.swift in Sources */ = {isa = PBXBuildFile; fileRef = C14527B42DE5FFB20026ACA8 /* MatchGeo.swift */; };
		C14527B72DE600E80026ACA8 /* StreakCalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C14527B62DE600E80026ACA8 /* StreakCalendarView.swift */; };
		C150FE172DD2D630004825A7 /* CloudImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C150FE142DD2D630004825A7 /* CloudImageView.swift */; };
		C150FE1A2DD5F929004825A7 /* GoalTimeSelectView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C150FE192DD5F929004825A7 /* GoalTimeSelectView.swift */; };
		C150FE202DD5FDB0004825A7 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = C150FE1F2DD5FDB0004825A7 /* Lottie */; };
		C190EAC12DB7B92A0052CC4F /* iCloudDocumentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAC02DB7B92A0052CC4F /* iCloudDocumentManager.swift */; };
		C190EAC32DB7BC650052CC4F /* StorageProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAC22DB7BC650052CC4F /* StorageProvider.swift */; };
		C190EAC52DB7E3B00052CC4F /* ImageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAC42DB7E3B00052CC4F /* ImageManager.swift */; };
		C190EACD2DB8CA000052CC4F /* WeekCalendarPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EACB2DB8CA000052CC4F /* WeekCalendarPicker.swift */; };
		C190EAD12DB916710052CC4F /* DayCalendarPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAD02DB916710052CC4F /* DayCalendarPicker.swift */; };
		C190EAD42DB91C740052CC4F /* YearSummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAD32DB91C740052CC4F /* YearSummaryView.swift */; };
		C190EAD52DB91C740052CC4F /* MonthSummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAD22DB91C740052CC4F /* MonthSummaryView.swift */; };
		C190EAD72DBB76010052CC4F /* MembershipManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAD62DBB76010052CC4F /* MembershipManager.swift */; };
		C190EAD92DBB76180052CC4F /* MembershipView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EAD82DBB76180052CC4F /* MembershipView.swift */; };
		C190EADB2DBBE5320052CC4F /* ColorThemeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EADA2DBBE52B0052CC4F /* ColorThemeManager.swift */; };
		C190EAE02DBBE9F30052CC4F /* SharePreviewFullScreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C190EADC2DBBE9F30052CC4F /* SharePreviewFullScreenView.swift */; };
		C196D76F2DE028DC005F94E4 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C196D76E2DE028DC005F94E4 /* LoadingView.swift */; };
		C19EB7232E0978FE00CA1076 /* GalleryGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C19EB7222E0978FE00CA1076 /* GalleryGridView.swift */; };
		C1ACA8662E03DBF20075D350 /* RingtoneSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1ACA8652E03DBF20075D350 /* RingtoneSelector.swift */; };
		C1ACA8712E03DFDB0075D350 /* whistle.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA86F2E03DFDB0075D350 /* whistle.mp3 */; };
		C1ACA8722E03DFDB0075D350 /* glow.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA86D2E03DFDB0075D350 /* glow.mp3 */; };
		C1ACA8732E03DFDB0075D350 /* rise.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA86A2E03DFDB0075D350 /* rise.mp3 */; };
		C1ACA8752E03DFDB0075D350 /* morning.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA8692E03DFDB0075D350 /* morning.mp3 */; };
		C1ACA8782E03DFDB0075D350 /* breeze.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA86C2E03DFDB0075D350 /* breeze.mp3 */; };
		C1ACA87E2E0444570075D350 /* glow.caf in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA87B2E0444570075D350 /* glow.caf */; };
		C1ACA87F2E0444570075D350 /* morning.caf in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA87C2E0444570075D350 /* morning.caf */; };
		C1ACA8802E0444570075D350 /* chime.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA87A2E0444570075D350 /* chime.mp3 */; };
		C1ACA8812E0444570075D350 /* breeze.caf in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA8792E0444570075D350 /* breeze.caf */; };
		C1ACA8822E0444570075D350 /* rise.caf in Resources */ = {isa = PBXBuildFile; fileRef = C1ACA87D2E0444570075D350 /* rise.caf */; };
		C1ADA4D22DEEA6710030DAF8 /* VideoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1ADA4D12DEEA6710030DAF8 /* VideoManager.swift */; };
		C1ADA4D42DEEAD560030DAF8 /* VideoItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1ADA4D32DEEAD560030DAF8 /* VideoItemView.swift */; };
		C1AF57F12DFEB02C0039A7CC /* CollectionItem+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1AF57F02DFEB02C0039A7CC /* CollectionItem+Extensions.swift */; };
		C1AF57F32DFEB7940039A7CC /* Color+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1AF57F22DFEB7940039A7CC /* Color+Extensions.swift */; };
		C1AF57F52DFEBE7A0039A7CC /* AlbumGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1AF57F42DFEBE7A0039A7CC /* AlbumGridView.swift */; };
		C1BF41692DAE8AA5009B7E6C /* TaskListManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1BF41682DAE8AA2009B7E6C /* TaskListManager.swift */; };
		C1BF416B2DAE8E81009B7E6C /* ConfigManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1BF416A2DAE8E7C009B7E6C /* ConfigManager.swift */; };
		C1E3C8A32DD219970068E4FA /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		C1E3C8A42DD219970068E4FA /* Sentry in Frameworks */ = {isa = PBXBuildFile; productRef = EA2A596CC2394E5BAB60C153 /* Sentry */; };
		C1E3C8A52DD219970068E4FA /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		C1E3C8A62DD219970068E4FA /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		C1E3C8A72DD219970068E4FA /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		C1EAE0A72E0C3453009F3D00 /* RecordingEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1EAE0A62E0C3453009F3D00 /* RecordingEditView.swift */; };
		C1FC68812DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */; };
		C1FC68822DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */; };
		C1FC68832DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */; };
		C1FC68842DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */; };
		C1FC68852DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */; };
		C1FC68882DCB2D9D00ECCF34 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = C1FC68872DCB2D9D00ECCF34 /* FirebaseAnalytics */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		57F474002BC697CF007E3315 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A98C06C42B651BB9005AC0F3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 57F473F02BC697CC007E3315;
			remoteInfo = PracticeLiveActivityWidgetExtension;
		};
		A93BF7B72BC151AF0074FA2B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A98C06C42B651BB9005AC0F3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A9F844002B842C8D00D35B12;
			remoteInfo = PracticeWidgetExtension;
		};
		A98C06DF2B651BBC005AC0F3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A98C06C42B651BB9005AC0F3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A98C06CB2B651BB9005AC0F3;
			remoteInfo = practice;
		};
		A98C06E92B651BBC005AC0F3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A98C06C42B651BB9005AC0F3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A98C06CB2B651BB9005AC0F3;
			remoteInfo = practice;
		};
		A9F844132B842C8F00D35B12 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A98C06C42B651BB9005AC0F3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A9F844002B842C8D00D35B12;
			remoteInfo = PracticeWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A9F844192B842C8F00D35B12 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				57F474022BC697CF007E3315 /* PracticeLiveActivityWidgetExtension.appex in Embed Foundation Extensions */,
				A9F844152B842C8F00D35B12 /* PracticeWidgetExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		573F78862BFB2CF8004D7EBC /* StatisticsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatisticsView.swift; sourceTree = "<group>"; };
		5751A5E52BAAE637004932E4 /* CalendarViewUI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarViewUI.swift; sourceTree = "<group>"; };
		5754638E2BB17F6800F8837A /* InfoCardGalleryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoCardGalleryView.swift; sourceTree = "<group>"; };
		575BDBC52D23C7460021C27C /* SubscribeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubscribeView.swift; sourceTree = "<group>"; };
		575BDBC72D23CD520021C27C /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		575BDBC92D23CE3E0021C27C /* Product.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = Product.storekit; sourceTree = "<group>"; };
		5761B5082BCE32C300E1D5A1 /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		576A9D132BB54F6B002A5949 /* CountdownTimerLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountdownTimerLiveActivity.swift; sourceTree = "<group>"; };
		576A9D1C2BBA8150002A5949 /* CountDownTimePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountDownTimePicker.swift; sourceTree = "<group>"; };
		576A9D1F2BBBA203002A5949 /* CountingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountingView.swift; sourceTree = "<group>"; };
		576A9D222BBBA6BF002A5949 /* CountDownContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountDownContainer.swift; sourceTree = "<group>"; };
		576A9D242BBC1B43002A5949 /* timeUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = timeUtils.swift; sourceTree = "<group>"; };
		576B36DB2C0DB0BC00462961 /* DailySummaryWidgetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DailySummaryWidgetView.swift; sourceTree = "<group>"; };
		576B36DD2C0DB1C700462961 /* DailySummaryWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DailySummaryWidget.swift; sourceTree = "<group>"; };
		57793C452C06D54E007D80A8 /* TaskItemData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskItemData.swift; sourceTree = "<group>"; };
		57793C472C06D560007D80A8 /* TaskProgressData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskProgressData.swift; sourceTree = "<group>"; };
		577F78F62B9EDAE9006C34CE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		577F78F72B9EFF59006C34CE /* UserPermission.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPermission.swift; sourceTree = "<group>"; };
		578197F82BB15EC10045F6C2 /* InfoCardCompactView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoCardCompactView.swift; sourceTree = "<group>"; };
		578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		57980D2A2BBD12320048CB64 /* CountupTimerLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountupTimerLiveActivity.swift; sourceTree = "<group>"; };
		57B005312BA153150095DEAC /* PracticeTime.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeTime.swift; sourceTree = "<group>"; };
		57B005332BA157010095DEAC /* TODOs.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TODOs.md; sourceTree = "<group>"; };
		57B005342BA19DCB0095DEAC /* ColorUnit.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorUnit.swift; sourceTree = "<group>"; };
		57B005392BA2F2610095DEAC /* PracticeTimeEditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeTimeEditView.swift; sourceTree = "<group>"; };
		57B4DE272BD10EBF000ABCC7 /* QRCode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QRCode.swift; sourceTree = "<group>"; };
		57C939F92BD2620900C391AB /* ShareImageFooter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareImageFooter.swift; sourceTree = "<group>"; };
		57D41A6E2BC8DDF600EDBDA0 /* ArcProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArcProgressView.swift; sourceTree = "<group>"; };
		57D41A712BC8FCAF00EDBDA0 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		57D41A732BC90A8D00EDBDA0 /* PersonalConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersonalConfig.swift; sourceTree = "<group>"; };
		57E7CA1B2BA425CD001BD416 /* TimeModifyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeModifyView.swift; sourceTree = "<group>"; };
		57F473EB2BC63579007E3315 /* PracticeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeView.swift; sourceTree = "<group>"; };
		57F473F12BC697CD007E3315 /* PracticeLiveActivityWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = PracticeLiveActivityWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		57F473F52BC697CD007E3315 /* PracticeLiveActivityWidgetBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeLiveActivityWidgetBundle.swift; sourceTree = "<group>"; };
		57F473F72BC697CD007E3315 /* PracticeLiveActivityWidgetLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeLiveActivityWidgetLiveActivity.swift; sourceTree = "<group>"; };
		57F473F92BC697CD007E3315 /* PracticeLiveActivityWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeLiveActivityWidget.swift; sourceTree = "<group>"; };
		57F473FB2BC697CD007E3315 /* AppIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppIntent.swift; sourceTree = "<group>"; };
		57F473FD2BC697CF007E3315 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		57F473FF2BC697CF007E3315 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A92CF0CD2B6BC46F00D48AC2 /* ProgressCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressCardView.swift; sourceTree = "<group>"; };
		A92CF0D02B6BDC2900D48AC2 /* TaskDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskDetailView.swift; sourceTree = "<group>"; };
		A92CF0D32B6D352700D48AC2 /* DateUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateUtils.swift; sourceTree = "<group>"; };
		A92CF0D52B6FD6CF00D48AC2 /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
		A93BF7B52BC11E230074FA2B /* InfoCardTimeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoCardTimeView.swift; sourceTree = "<group>"; };
		A96344822B67E1B50058F857 /* TaskItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskItem.swift; sourceTree = "<group>"; };
		A97FFB262B72834500AED8F8 /* AddNewAchievementView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddNewAchievementView.swift; sourceTree = "<group>"; };
		A98C06CC2B651BB9005AC0F3 /* practice.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = practice.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A98C06CF2B651BB9005AC0F3 /* practiceApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = practiceApp.swift; sourceTree = "<group>"; };
		A98C06D12B651BB9005AC0F3 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A98C06D52B651BBC005AC0F3 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A98C06D82B651BBC005AC0F3 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A98C06DE2B651BBC005AC0F3 /* practiceTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = practiceTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A98C06E82B651BBC005AC0F3 /* practiceUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = practiceUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A98C07002B652232005AC0F3 /* CurrentListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrentListView.swift; sourceTree = "<group>"; };
		A98C07062B652E45005AC0F3 /* AddNewItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddNewItemView.swift; sourceTree = "<group>"; };
		A98C070C2B653A7B005AC0F3 /* AchievementListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AchievementListView.swift; sourceTree = "<group>"; };
		A98C070F2B654321005AC0F3 /* InfoCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoCardView.swift; sourceTree = "<group>"; };
		A9D74C1B2B97702C00A92E3C /* TimerScrollSelect.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimerScrollSelect.swift; sourceTree = "<group>"; };
		A9F843FB2B8384F900D35B12 /* ProgressYearCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressYearCard.swift; sourceTree = "<group>"; };
		A9F844012B842C8D00D35B12 /* PracticeWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = PracticeWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		A9F844032B842C8D00D35B12 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		A9F844052B842C8D00D35B12 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		A9F844082B842C8D00D35B12 /* PracticeWidgetBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeWidgetBundle.swift; sourceTree = "<group>"; };
		A9F8440A2B842C8D00D35B12 /* PracticeWidgetLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeWidgetLiveActivity.swift; sourceTree = "<group>"; };
		A9F8440C2B842C8D00D35B12 /* PracticeWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PracticeWidget.swift; sourceTree = "<group>"; };
		A9F8440E2B842C8D00D35B12 /* YearProcessWidgetIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YearProcessWidgetIntent.swift; sourceTree = "<group>"; };
		A9F844102B842C8F00D35B12 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A9F844122B842C8F00D35B12 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A9F844222B84C8E200D35B12 /* practice.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = practice.entitlements; sourceTree = "<group>"; };
		A9F844232B84CA8A00D35B12 /* PracticeWidgetExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = PracticeWidgetExtension.entitlements; sourceTree = "<group>"; };
		A9F8442C2B84D8CD00D35B12 /* YearProcessWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YearProcessWidget.swift; sourceTree = "<group>"; };
		A9F8444D2B8E12DB00D35B12 /* CountDownView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CountDownView.swift; sourceTree = "<group>"; };
		A9F8444F2B8E315100D35B12 /* MonthCalendarProgressCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonthCalendarProgressCardView.swift; sourceTree = "<group>"; };
		C105312C2DE89FB200B3A029 /* AppGlobalStateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppGlobalStateManager.swift; sourceTree = "<group>"; };
		C10DEDB92DF9B6CF007828F5 /* readme.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		C10DEDBE2DF9C3BB007828F5 /* CollectionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionManager.swift; sourceTree = "<group>"; };
		C10DEDC12DF9C3E0007828F5 /* CollectionListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionListView.swift; sourceTree = "<group>"; };
		C10DEDC62DF9C8B2007828F5 /* CollectionDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionDetailView.swift; sourceTree = "<group>"; };
		C10DEDC72DF9C8B2007828F5 /* CreateCollectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateCollectionView.swift; sourceTree = "<group>"; };
		C10DEDCA2DFA5719007828F5 /* CollectionItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionItemView.swift; sourceTree = "<group>"; };
		C10DEDCC2DFA58D4007828F5 /* TaskCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskCardView.swift; sourceTree = "<group>"; };
		C10DEDCE2DFA7581007828F5 /* CollectionSortView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionSortView.swift; sourceTree = "<group>"; };
		C10DEDD32DFAA82A007828F5 /* ScrollableCoverView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScrollableCoverView.swift; sourceTree = "<group>"; };
		C10DEDD52DFAB4D0007828F5 /* ListCoverView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ListCoverView.swift; sourceTree = "<group>"; };
		C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaurelTimeViewSmall.swift; sourceTree = "<group>"; };
		C114529D2DAEA21900E54806 /* RatingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RatingManager.swift; sourceTree = "<group>"; };
		C114529F2DAF637A00E54806 /* RecordingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingManager.swift; sourceTree = "<group>"; };
		C11452A52DAFA90C00E54806 /* RecordingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingView.swift; sourceTree = "<group>"; };
		C11452A72DAFB12300E54806 /* Waveform.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = Waveform.swift; path = practice/BasicComponent/Waveform.swift; sourceTree = SOURCE_ROOT; };
		C11452AE2DB0EB5E00E54806 /* PlaybackSliderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaybackSliderView.swift; sourceTree = "<group>"; };
		C11452B02DB105E200E54806 /* RecordingItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingItemView.swift; sourceTree = "<group>"; };
		C11537D22DDD891300897938 /* CustomFontSize.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomFontSize.swift; sourceTree = "<group>"; };
		C11976572DB10C000079670B /* LaurelTimeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaurelTimeView.swift; sourceTree = "<group>"; };
		C119765C2DB165A30079670B /* CustomAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomAlertView.swift; sourceTree = "<group>"; };
		C11976612DB384FC0079670B /* TaskStatisticsNavigator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskStatisticsNavigator.swift; sourceTree = "<group>"; };
		C11976662DB50DF20079670B /* NavigationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationManager.swift; sourceTree = "<group>"; };
		C11976682DB524DE0079670B /* ActionSheetWrapper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActionSheetWrapper.swift; sourceTree = "<group>"; };
		C119766A2DB5482C0079670B /* TaskManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskManager.swift; sourceTree = "<group>"; };
		C119766C2DB63BA50079670B /* DaySummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DaySummaryView.swift; sourceTree = "<group>"; };
		C119766E2DB63BF10079670B /* WeekSummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeekSummaryView.swift; sourceTree = "<group>"; };
		C122C0482DD987B60004E0A7 /* ChildShape.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = ChildShape.swift; path = practice/BasicComponent/ChildShape.swift; sourceTree = "<group>"; };
		C122C04D2DDAC34B0004E0A7 /* StreakCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StreakCardView.swift; sourceTree = "<group>"; };
		C122C0502DDACF110004E0A7 /* Nunito-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Black.ttf"; sourceTree = "<group>"; };
		C122C0512DDACF110004E0A7 /* Nunito-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Bold.ttf"; sourceTree = "<group>"; };
		C122C0522DDACF110004E0A7 /* Nunito-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Medium.ttf"; sourceTree = "<group>"; };
		C122C0532DDACF110004E0A7 /* Nunito-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Regular.ttf"; sourceTree = "<group>"; };
		C122C0542DDACF110004E0A7 /* Nunito-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-SemiBold.ttf"; sourceTree = "<group>"; };
		C122C05A2DDAE8080004E0A7 /* TodayProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TodayProgressView.swift; sourceTree = "<group>"; };
		C1256BD12DDB694B006DC118 /* TaskInfoCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskInfoCard.swift; sourceTree = "<group>"; };
		C1256BD32DDB731A006DC118 /* NotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationManager.swift; sourceTree = "<group>"; };
		C1256BD52DDB8071006DC118 /* StepGuider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepGuider.swift; sourceTree = "<group>"; };
		C1256BD72DDB827D006DC118 /* OnboardingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingView.swift; sourceTree = "<group>"; };
		C1256BD92DDB8668006DC118 /* ReminderTimeSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReminderTimeSelector.swift; sourceTree = "<group>"; };
		C1256BDB2DDBB84C006DC118 /* CustomSubscribeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomSubscribeView.swift; sourceTree = "<group>"; };
		C1256BDD2DDC4989006DC118 /* AnimatableNumber.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnimatableNumber.swift; sourceTree = "<group>"; };
		C1256BE02DDC7148006DC118 /* HapticManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HapticManager.swift; sourceTree = "<group>"; };
		C1256BE22DDD6F8A006DC118 /* ViewExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewExtension.swift; sourceTree = "<group>"; };
		C14527B42DE5FFB20026ACA8 /* MatchGeo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MatchGeo.swift; sourceTree = "<group>"; };
		C14527B62DE600E80026ACA8 /* StreakCalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StreakCalendarView.swift; sourceTree = "<group>"; };
		C150FE142DD2D630004825A7 /* CloudImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloudImageView.swift; sourceTree = "<group>"; };
		C150FE192DD5F929004825A7 /* GoalTimeSelectView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalTimeSelectView.swift; sourceTree = "<group>"; };
		C190EAC02DB7B92A0052CC4F /* iCloudDocumentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = iCloudDocumentManager.swift; sourceTree = "<group>"; };
		C190EAC22DB7BC650052CC4F /* StorageProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StorageProvider.swift; sourceTree = "<group>"; };
		C190EAC42DB7E3B00052CC4F /* ImageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageManager.swift; sourceTree = "<group>"; };
		C190EACB2DB8CA000052CC4F /* WeekCalendarPicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeekCalendarPicker.swift; sourceTree = "<group>"; };
		C190EAD02DB916710052CC4F /* DayCalendarPicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DayCalendarPicker.swift; sourceTree = "<group>"; };
		C190EAD22DB91C740052CC4F /* MonthSummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonthSummaryView.swift; sourceTree = "<group>"; };
		C190EAD32DB91C740052CC4F /* YearSummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YearSummaryView.swift; sourceTree = "<group>"; };
		C190EAD62DBB76010052CC4F /* MembershipManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MembershipManager.swift; sourceTree = "<group>"; };
		C190EAD82DBB76180052CC4F /* MembershipView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MembershipView.swift; sourceTree = "<group>"; };
		C190EADA2DBBE52B0052CC4F /* ColorThemeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorThemeManager.swift; sourceTree = "<group>"; };
		C190EADC2DBBE9F30052CC4F /* SharePreviewFullScreenView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharePreviewFullScreenView.swift; sourceTree = "<group>"; };
		C196D76E2DE028DC005F94E4 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		C19EB7222E0978FE00CA1076 /* GalleryGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GalleryGridView.swift; sourceTree = "<group>"; };
		C1ACA8652E03DBF20075D350 /* RingtoneSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RingtoneSelector.swift; sourceTree = "<group>"; };
		C1ACA8692E03DFDB0075D350 /* morning.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = morning.mp3; sourceTree = "<group>"; };
		C1ACA86A2E03DFDB0075D350 /* rise.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = rise.mp3; sourceTree = "<group>"; };
		C1ACA86C2E03DFDB0075D350 /* breeze.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = breeze.mp3; sourceTree = "<group>"; };
		C1ACA86D2E03DFDB0075D350 /* glow.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = glow.mp3; sourceTree = "<group>"; };
		C1ACA86F2E03DFDB0075D350 /* whistle.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = whistle.mp3; sourceTree = "<group>"; };
		C1ACA8792E0444570075D350 /* breeze.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = breeze.caf; sourceTree = "<group>"; };
		C1ACA87A2E0444570075D350 /* chime.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = chime.mp3; sourceTree = "<group>"; };
		C1ACA87B2E0444570075D350 /* glow.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = glow.caf; sourceTree = "<group>"; };
		C1ACA87C2E0444570075D350 /* morning.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = morning.caf; sourceTree = "<group>"; };
		C1ACA87D2E0444570075D350 /* rise.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = rise.caf; sourceTree = "<group>"; };
		C1ADA4D12DEEA6710030DAF8 /* VideoManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoManager.swift; sourceTree = "<group>"; };
		C1ADA4D32DEEAD560030DAF8 /* VideoItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoItemView.swift; sourceTree = "<group>"; };
		C1AF57F02DFEB02C0039A7CC /* CollectionItem+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "CollectionItem+Extensions.swift"; sourceTree = "<group>"; };
		C1AF57F22DFEB7940039A7CC /* Color+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+Extensions.swift"; sourceTree = "<group>"; };
		C1AF57F42DFEBE7A0039A7CC /* AlbumGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlbumGridView.swift; sourceTree = "<group>"; };
		C1BF41682DAE8AA2009B7E6C /* TaskListManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskListManager.swift; sourceTree = "<group>"; };
		C1BF416A2DAE8E7C009B7E6C /* ConfigManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigManager.swift; sourceTree = "<group>"; };
		C1EAE0A62E0C3453009F3D00 /* RecordingEditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordingEditView.swift; sourceTree = "<group>"; };
		C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		57F473EE2BC697CC007E3315 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57F473F32BC697CD007E3315 /* SwiftUI.framework in Frameworks */,
				57F473F22BC697CD007E3315 /* WidgetKit.framework in Frameworks */,
				C1E3C8A52DD219970068E4FA /* (null) in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06C92B651BB9005AC0F3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C1E3C8A42DD219970068E4FA /* Sentry in Frameworks */,
				570D6F822BA4284700BA0C0A /* HorizonCalendar in Frameworks */,
				C150FE202DD5FDB0004825A7 /* Lottie in Frameworks */,
				575BDBC82D23CD520021C27C /* StoreKit.framework in Frameworks */,
				C142722D2DCE3F1800EBB0B6 /* FirebasePerformance in Frameworks */,
				C142722A2DCE3B9300EBB0B6 /* FirebaseCrashlytics in Frameworks */,
				C1FC68882DCB2D9D00ECCF34 /* FirebaseAnalytics in Frameworks */,
				57E21D732BDB4FD300CC6072 /* ConfettiSwiftUI in Frameworks */,
				C1E3C8A32DD219970068E4FA /* (null) in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06DB2B651BBC005AC0F3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C1E3C8A62DD219970068E4FA /* (null) in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06E52B651BBC005AC0F3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C1E3C8A72DD219970068E4FA /* (null) in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9F843FE2B842C8D00D35B12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A9F844062B842C8D00D35B12 /* SwiftUI.framework in Frameworks */,
				A9F844042B842C8D00D35B12 /* WidgetKit.framework in Frameworks */,
				54F20DBE172F44D89E8F0A0B /* Sentry in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		570D6F832BA428EA00BA0C0A /* BasicComponent */ = {
			isa = PBXGroup;
			children = (
				C10DEDD32DFAA82A007828F5 /* ScrollableCoverView.swift */,
				C10DEDD52DFAB4D0007828F5 /* ListCoverView.swift */,
				C11452A72DAFB12300E54806 /* Waveform.swift */,
				C11976572DB10C000079670B /* LaurelTimeView.swift */,
				C10DEDD72DFAB8C0007828F5 /* LaurelTimeViewSmall.swift */,
				C119765C2DB165A30079670B /* CustomAlertView.swift */,
				C190EACB2DB8CA000052CC4F /* WeekCalendarPicker.swift */,
				C190EAD02DB916710052CC4F /* DayCalendarPicker.swift */,
				C11976682DB524DE0079670B /* ActionSheetWrapper.swift */,
				5751A5E52BAAE637004932E4 /* CalendarViewUI.swift */,
				57D41A6E2BC8DDF600EDBDA0 /* ArcProgressView.swift */,
				C11452AE2DB0EB5E00E54806 /* PlaybackSliderView.swift */,
				C150FE142DD2D630004825A7 /* CloudImageView.swift */,
				C1256BD52DDB8071006DC118 /* StepGuider.swift */,
				C1256BDD2DDC4989006DC118 /* AnimatableNumber.swift */,
			);
			path = BasicComponent;
			sourceTree = "<group>";
		};
		573F78852BFB2CDF004D7EBC /* Statistics */ = {
			isa = PBXGroup;
			children = (
				C190EAD22DB91C740052CC4F /* MonthSummaryView.swift */,
				C190EAD32DB91C740052CC4F /* YearSummaryView.swift */,
				C119766C2DB63BA50079670B /* DaySummaryView.swift */,
				C119766E2DB63BF10079670B /* WeekSummaryView.swift */,
				573F78862BFB2CF8004D7EBC /* StatisticsView.swift */,
			);
			path = Statistics;
			sourceTree = "<group>";
		};
		575BDBC42D23C7250021C27C /* Subscribe */ = {
			isa = PBXGroup;
			children = (
				575BDBC52D23C7460021C27C /* SubscribeView.swift */,
				C1256BDB2DDBB84C006DC118 /* CustomSubscribeView.swift */,
			);
			path = Subscribe;
			sourceTree = "<group>";
		};
		576A9D1E2BBAB8EF002A5949 /* CountDown */ = {
			isa = PBXGroup;
			children = (
				576A9D222BBBA6BF002A5949 /* CountDownContainer.swift */,
				A9D74C1B2B97702C00A92E3C /* TimerScrollSelect.swift */,
				576A9D1F2BBBA203002A5949 /* CountingView.swift */,
				A9F8444D2B8E12DB00D35B12 /* CountDownView.swift */,
				576A9D1C2BBA8150002A5949 /* CountDownTimePicker.swift */,
				576A9D132BB54F6B002A5949 /* CountdownTimerLiveActivity.swift */,
				57980D2A2BBD12320048CB64 /* CountupTimerLiveActivity.swift */,
			);
			path = CountDown;
			sourceTree = "<group>";
		};
		576B36D92C0DAFB500462961 /* YearProcess */ = {
			isa = PBXGroup;
			children = (
				A9F8440E2B842C8D00D35B12 /* YearProcessWidgetIntent.swift */,
				A9F8442C2B84D8CD00D35B12 /* YearProcessWidget.swift */,
				A9F8440C2B842C8D00D35B12 /* PracticeWidget.swift */,
			);
			path = YearProcess;
			sourceTree = "<group>";
		};
		576B36DA2C0DB04000462961 /* DailySummary */ = {
			isa = PBXGroup;
			children = (
				576B36DB2C0DB0BC00462961 /* DailySummaryWidgetView.swift */,
				576B36DD2C0DB1C700462961 /* DailySummaryWidget.swift */,
			);
			path = DailySummary;
			sourceTree = "<group>";
		};
		57793C442C06D4FB007D80A8 /* Data */ = {
			isa = PBXGroup;
			children = (
				57793C452C06D54E007D80A8 /* TaskItemData.swift */,
				57793C472C06D560007D80A8 /* TaskProgressData.swift */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		57C939F82BD261E100C391AB /* ShareContent */ = {
			isa = PBXGroup;
			children = (
				57C939F92BD2620900C391AB /* ShareImageFooter.swift */,
			);
			path = ShareContent;
			sourceTree = "<group>";
		};
		57D41A702BC8FCA700EDBDA0 /* Settings */ = {
			isa = PBXGroup;
			children = (
				C1ACA8652E03DBF20075D350 /* RingtoneSelector.swift */,
				57D41A712BC8FCAF00EDBDA0 /* SettingsView.swift */,
				C1256BD92DDB8668006DC118 /* ReminderTimeSelector.swift */,
				C150FE192DD5F929004825A7 /* GoalTimeSelectView.swift */,
				C1256BD72DDB827D006DC118 /* OnboardingView.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		57F473EA2BC63567007E3315 /* PracticeView */ = {
			isa = PBXGroup;
			children = (
				57F473EB2BC63579007E3315 /* PracticeView.swift */,
				C122C04D2DDAC34B0004E0A7 /* StreakCardView.swift */,
				C14527B62DE600E80026ACA8 /* StreakCalendarView.swift */,
				C122C05A2DDAE8080004E0A7 /* TodayProgressView.swift */,
			);
			path = PracticeView;
			sourceTree = "<group>";
		};
		57F473F42BC697CD007E3315 /* PracticeLiveActivityWidget */ = {
			isa = PBXGroup;
			children = (
				A9F8440A2B842C8D00D35B12 /* PracticeWidgetLiveActivity.swift */,
				57F473F52BC697CD007E3315 /* PracticeLiveActivityWidgetBundle.swift */,
				57F473F72BC697CD007E3315 /* PracticeLiveActivityWidgetLiveActivity.swift */,
				57F473F92BC697CD007E3315 /* PracticeLiveActivityWidget.swift */,
				57F473FB2BC697CD007E3315 /* AppIntent.swift */,
				57F473FD2BC697CF007E3315 /* Assets.xcassets */,
				57F473FF2BC697CF007E3315 /* Info.plist */,
			);
			path = PracticeLiveActivityWidget;
			sourceTree = "<group>";
		};
		A92CF0CC2B6BC45000D48AC2 /* ProgressCard */ = {
			isa = PBXGroup;
			children = (
				A92CF0CD2B6BC46F00D48AC2 /* ProgressCardView.swift */,
				A9F8444F2B8E315100D35B12 /* MonthCalendarProgressCardView.swift */,
			);
			path = ProgressCard;
			sourceTree = "<group>";
		};
		A92CF0CF2B6BDC1800D48AC2 /* TaskDetail */ = {
			isa = PBXGroup;
			children = (
				C1EAE0A62E0C3453009F3D00 /* RecordingEditView.swift */,
				C11976612DB384FC0079670B /* TaskStatisticsNavigator.swift */,
				C11452B02DB105E200E54806 /* RecordingItemView.swift */,
				A92CF0D02B6BDC2900D48AC2 /* TaskDetailView.swift */,
				C1ADA4D32DEEAD560030DAF8 /* VideoItemView.swift */,
				57B005392BA2F2610095DEAC /* PracticeTimeEditView.swift */,
				57E7CA1B2BA425CD001BD416 /* TimeModifyView.swift */,
			);
			path = TaskDetail;
			sourceTree = "<group>";
		};
		A92CF0D22B6D350C00D48AC2 /* Utils */ = {
			isa = PBXGroup;
			children = (
				A92CF0D32B6D352700D48AC2 /* DateUtils.swift */,
				577F78F72B9EFF59006C34CE /* UserPermission.swift */,
				57B005312BA153150095DEAC /* PracticeTime.swift */,
				57B005342BA19DCB0095DEAC /* ColorUnit.swift */,
				576A9D242BBC1B43002A5949 /* timeUtils.swift */,
				57B4DE272BD10EBF000ABCC7 /* QRCode.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		A96344812B6726B80058F857 /* Model */ = {
			isa = PBXGroup;
			children = (
				A96344822B67E1B50058F857 /* TaskItem.swift */,
				57D41A732BC90A8D00EDBDA0 /* PersonalConfig.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		A98C06C32B651BB9005AC0F3 = {
			isa = PBXGroup;
			children = (
				C10DEDCE2DFA7581007828F5 /* CollectionSortView.swift */,
				C10DEDB92DF9B6CF007828F5 /* readme.md */,
				C122C0482DD987B60004E0A7 /* ChildShape.swift */,
				575BDBC92D23CE3E0021C27C /* Product.storekit */,
				578920F32BB410DC0061093B /* PrivacyInfo.xcprivacy */,
				A9F844232B84CA8A00D35B12 /* PracticeWidgetExtension.entitlements */,
				A98C06CE2B651BB9005AC0F3 /* practice */,
				A9F844072B842C8D00D35B12 /* PracticeWidget */,
				57F473F42BC697CD007E3315 /* PracticeLiveActivityWidget */,
				A9F844022B842C8D00D35B12 /* Frameworks */,
				A98C06CD2B651BB9005AC0F3 /* Products */,
			);
			sourceTree = "<group>";
		};
		A98C06CD2B651BB9005AC0F3 /* Products */ = {
			isa = PBXGroup;
			children = (
				A98C06CC2B651BB9005AC0F3 /* practice.app */,
				A98C06DE2B651BBC005AC0F3 /* practiceTests.xctest */,
				A98C06E82B651BBC005AC0F3 /* practiceUITests.xctest */,
				A9F844012B842C8D00D35B12 /* PracticeWidgetExtension.appex */,
				57F473F12BC697CD007E3315 /* PracticeLiveActivityWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A98C06CE2B651BB9005AC0F3 /* practice */ = {
			isa = PBXGroup;
			children = (
				C1ACA8702E03DFDB0075D350 /* Resources */,
				C14527B32DE5FFA20026ACA8 /* Demo */,
				C1256BDF2DDC7135006DC118 /* Extension */,
				C122C04F2DDACEEC0004E0A7 /* Font */,
				C1BF41672DAE8A99009B7E6C /* ViewModels */,
				57793C442C06D4FB007D80A8 /* Data */,
				570D6F832BA428EA00BA0C0A /* BasicComponent */,
				A92CF0D22B6D350C00D48AC2 /* Utils */,
				A96344812B6726B80058F857 /* Model */,
				A98C06FB2B652063005AC0F3 /* View */,
				A98C06D72B651BBC005AC0F3 /* Preview Content */,
				A98C06CF2B651BB9005AC0F3 /* practiceApp.swift */,
				A98C06D12B651BB9005AC0F3 /* ContentView.swift */,
				A92CF0D52B6FD6CF00D48AC2 /* Launch Screen.storyboard */,
				A98C06D52B651BBC005AC0F3 /* Assets.xcassets */,
				5761B5082BCE32C300E1D5A1 /* Localizable.xcstrings */,
				A9F844222B84C8E200D35B12 /* practice.entitlements */,
				577F78F62B9EDAE9006C34CE /* Info.plist */,
				C1FC68802DCB280A00ECCF34 /* GoogleService-Info.plist */,
				57B005332BA157010095DEAC /* TODOs.md */,
			);
			path = practice;
			sourceTree = "<group>";
		};
		A98C06D72B651BBC005AC0F3 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A98C06D82B651BBC005AC0F3 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A98C06FB2B652063005AC0F3 /* View */ = {
			isa = PBXGroup;
			children = (
				C190EAD82DBB76180052CC4F /* MembershipView.swift */,
				C196D76E2DE028DC005F94E4 /* LoadingView.swift */,
				C11452A42DAFA8F100E54806 /* Recording */,
				575BDBC42D23C7250021C27C /* Subscribe */,
				573F78852BFB2CDF004D7EBC /* Statistics */,
				57D41A702BC8FCA700EDBDA0 /* Settings */,
				57F473EA2BC63567007E3315 /* PracticeView */,
				576A9D1E2BBAB8EF002A5949 /* CountDown */,
				A9F843FA2B8384D400D35B12 /* ProgressYearCard */,
				A92CF0CF2B6BDC1800D48AC2 /* TaskDetail */,
				A92CF0CC2B6BC45000D48AC2 /* ProgressCard */,
				A98C070E2B65430B005AC0F3 /* InfoCard */,
				A98C070B2B653A63005AC0F3 /* AchievementList */,
				A98C07052B652E38005AC0F3 /* NewItemView */,
				A98C06FF2B652215005AC0F3 /* CurrentList */,
				57C939F82BD261E100C391AB /* ShareContent */,
			);
			path = View;
			sourceTree = "<group>";
		};
		A98C06FF2B652215005AC0F3 /* CurrentList */ = {
			isa = PBXGroup;
			children = (
				A98C07002B652232005AC0F3 /* CurrentListView.swift */,
			);
			path = CurrentList;
			sourceTree = "<group>";
		};
		A98C07052B652E38005AC0F3 /* NewItemView */ = {
			isa = PBXGroup;
			children = (
				A98C07062B652E45005AC0F3 /* AddNewItemView.swift */,
				A97FFB262B72834500AED8F8 /* AddNewAchievementView.swift */,
			);
			path = NewItemView;
			sourceTree = "<group>";
		};
		A98C070B2B653A63005AC0F3 /* AchievementList */ = {
			isa = PBXGroup;
			children = (
				C1AF57F42DFEBE7A0039A7CC /* AlbumGridView.swift */,
				C10DEDC62DF9C8B2007828F5 /* CollectionDetailView.swift */,
				C19EB7222E0978FE00CA1076 /* GalleryGridView.swift */,
				C10DEDC72DF9C8B2007828F5 /* CreateCollectionView.swift */,
				C190EADC2DBBE9F30052CC4F /* SharePreviewFullScreenView.swift */,
				A98C070C2B653A7B005AC0F3 /* AchievementListView.swift */,
				C10DEDC12DF9C3E0007828F5 /* CollectionListView.swift */,
				C10DEDCA2DFA5719007828F5 /* CollectionItemView.swift */,
			);
			path = AchievementList;
			sourceTree = "<group>";
		};
		A98C070E2B65430B005AC0F3 /* InfoCard */ = {
			isa = PBXGroup;
			children = (
				C10DEDCC2DFA58D4007828F5 /* TaskCardView.swift */,
				A98C070F2B654321005AC0F3 /* InfoCardView.swift */,
				578197F82BB15EC10045F6C2 /* InfoCardCompactView.swift */,
				5754638E2BB17F6800F8837A /* InfoCardGalleryView.swift */,
				A93BF7B52BC11E230074FA2B /* InfoCardTimeView.swift */,
				C1256BD12DDB694B006DC118 /* TaskInfoCard.swift */,
			);
			path = InfoCard;
			sourceTree = "<group>";
		};
		A9F843FA2B8384D400D35B12 /* ProgressYearCard */ = {
			isa = PBXGroup;
			children = (
				A9F843FB2B8384F900D35B12 /* ProgressYearCard.swift */,
			);
			path = ProgressYearCard;
			sourceTree = "<group>";
		};
		A9F844022B842C8D00D35B12 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				575BDBC72D23CD520021C27C /* StoreKit.framework */,
				A9F844032B842C8D00D35B12 /* WidgetKit.framework */,
				A9F844052B842C8D00D35B12 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A9F844072B842C8D00D35B12 /* PracticeWidget */ = {
			isa = PBXGroup;
			children = (
				C1FC68892DCCF5BC00ECCF34 /* TotalPracticeTime */,
				576B36DA2C0DB04000462961 /* DailySummary */,
				576B36D92C0DAFB500462961 /* YearProcess */,
				A9F844082B842C8D00D35B12 /* PracticeWidgetBundle.swift */,
				A9F844102B842C8F00D35B12 /* Assets.xcassets */,
				A9F844122B842C8F00D35B12 /* Info.plist */,
			);
			path = PracticeWidget;
			sourceTree = "<group>";
		};
		C11452A42DAFA8F100E54806 /* Recording */ = {
			isa = PBXGroup;
			children = (
				C11452A52DAFA90C00E54806 /* RecordingView.swift */,
			);
			path = Recording;
			sourceTree = "<group>";
		};
		C122C04F2DDACEEC0004E0A7 /* Font */ = {
			isa = PBXGroup;
			children = (
				C122C0502DDACF110004E0A7 /* Nunito-Black.ttf */,
				C122C0512DDACF110004E0A7 /* Nunito-Bold.ttf */,
				C122C0522DDACF110004E0A7 /* Nunito-Medium.ttf */,
				C122C0532DDACF110004E0A7 /* Nunito-Regular.ttf */,
				C122C0542DDACF110004E0A7 /* Nunito-SemiBold.ttf */,
				C11537D22DDD891300897938 /* CustomFontSize.swift */,
			);
			path = Font;
			sourceTree = "<group>";
		};
		C1256BDF2DDC7135006DC118 /* Extension */ = {
			isa = PBXGroup;
			children = (
				C1AF57F02DFEB02C0039A7CC /* CollectionItem+Extensions.swift */,
				C1256BE02DDC7148006DC118 /* HapticManager.swift */,
				C1256BE22DDD6F8A006DC118 /* ViewExtension.swift */,
				C1AF57F22DFEB7940039A7CC /* Color+Extensions.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		C14527B32DE5FFA20026ACA8 /* Demo */ = {
			isa = PBXGroup;
			children = (
				C14527B42DE5FFB20026ACA8 /* MatchGeo.swift */,
			);
			path = Demo;
			sourceTree = "<group>";
		};
		C1ACA8702E03DFDB0075D350 /* Resources */ = {
			isa = PBXGroup;
			children = (
				C1ACA8792E0444570075D350 /* breeze.caf */,
				C1ACA87A2E0444570075D350 /* chime.mp3 */,
				C1ACA87B2E0444570075D350 /* glow.caf */,
				C1ACA87C2E0444570075D350 /* morning.caf */,
				C1ACA87D2E0444570075D350 /* rise.caf */,
				C1ACA8692E03DFDB0075D350 /* morning.mp3 */,
				C1ACA86A2E03DFDB0075D350 /* rise.mp3 */,
				C1ACA86C2E03DFDB0075D350 /* breeze.mp3 */,
				C1ACA86D2E03DFDB0075D350 /* glow.mp3 */,
				C1ACA86F2E03DFDB0075D350 /* whistle.mp3 */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		C1BF41672DAE8A99009B7E6C /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				C190EADA2DBBE52B0052CC4F /* ColorThemeManager.swift */,
				C190EAD62DBB76010052CC4F /* MembershipManager.swift */,
				C114529F2DAF637A00E54806 /* RecordingManager.swift */,
				C190EAC02DB7B92A0052CC4F /* iCloudDocumentManager.swift */,
				C190EAC22DB7BC650052CC4F /* StorageProvider.swift */,
				C114529D2DAEA21900E54806 /* RatingManager.swift */,
				C1BF416A2DAE8E7C009B7E6C /* ConfigManager.swift */,
				C1BF41682DAE8AA2009B7E6C /* TaskListManager.swift */,
				C11976662DB50DF20079670B /* NavigationManager.swift */,
				C119766A2DB5482C0079670B /* TaskManager.swift */,
				C190EAC42DB7E3B00052CC4F /* ImageManager.swift */,
				C1256BD32DDB731A006DC118 /* NotificationManager.swift */,
				C1ADA4D12DEEA6710030DAF8 /* VideoManager.swift */,
				C105312C2DE89FB200B3A029 /* AppGlobalStateManager.swift */,
				C10DEDBE2DF9C3BB007828F5 /* CollectionManager.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		C1FC68892DCCF5BC00ECCF34 /* TotalPracticeTime */ = {
			isa = PBXGroup;
			children = (
			);
			path = TotalPracticeTime;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		57F473F02BC697CC007E3315 /* PracticeLiveActivityWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 57F474032BC697CF007E3315 /* Build configuration list for PBXNativeTarget "PracticeLiveActivityWidgetExtension" */;
			buildPhases = (
				57F473ED2BC697CC007E3315 /* Sources */,
				57F473EE2BC697CC007E3315 /* Frameworks */,
				57F473EF2BC697CC007E3315 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PracticeLiveActivityWidgetExtension;
			productName = PracticeLiveActivityWidgetExtension;
			productReference = 57F473F12BC697CD007E3315 /* PracticeLiveActivityWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		A98C06CB2B651BB9005AC0F3 /* practice */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A98C06F22B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practice" */;
			buildPhases = (
				A98C06C82B651BB9005AC0F3 /* Sources */,
				A98C06C92B651BB9005AC0F3 /* Frameworks */,
				A98C06CA2B651BB9005AC0F3 /* Resources */,
				A9F844192B842C8F00D35B12 /* Embed Foundation Extensions */,
				C142722B2DCE3C5400EBB0B6 /* ShellScript */,
				712872F68F2341B784F22A97 /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
				A9F844142B842C8F00D35B12 /* PBXTargetDependency */,
				A93BF7B82BC151AF0074FA2B /* PBXTargetDependency */,
				57F474012BC697CF007E3315 /* PBXTargetDependency */,
			);
			name = practice;
			packageProductDependencies = (
				570D6F812BA4284700BA0C0A /* HorizonCalendar */,
				57E21D722BDB4FD300CC6072 /* ConfettiSwiftUI */,
				C1FC68872DCB2D9D00ECCF34 /* FirebaseAnalytics */,
				C14272292DCE3B9300EBB0B6 /* FirebaseCrashlytics */,
				C142722C2DCE3F1800EBB0B6 /* FirebasePerformance */,
				EA2A596CC2394E5BAB60C153 /* Sentry */,
				C150FE1F2DD5FDB0004825A7 /* Lottie */,
			);
			productName = practice;
			productReference = A98C06CC2B651BB9005AC0F3 /* practice.app */;
			productType = "com.apple.product-type.application";
		};
		A98C06DD2B651BBC005AC0F3 /* practiceTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A98C06F52B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practiceTests" */;
			buildPhases = (
				A98C06DA2B651BBC005AC0F3 /* Sources */,
				A98C06DB2B651BBC005AC0F3 /* Frameworks */,
				A98C06DC2B651BBC005AC0F3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A98C06E02B651BBC005AC0F3 /* PBXTargetDependency */,
			);
			name = practiceTests;
			productName = practiceTests;
			productReference = A98C06DE2B651BBC005AC0F3 /* practiceTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A98C06E72B651BBC005AC0F3 /* practiceUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A98C06F82B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practiceUITests" */;
			buildPhases = (
				A98C06E42B651BBC005AC0F3 /* Sources */,
				A98C06E52B651BBC005AC0F3 /* Frameworks */,
				A98C06E62B651BBC005AC0F3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A98C06EA2B651BBC005AC0F3 /* PBXTargetDependency */,
			);
			name = practiceUITests;
			productName = practiceUITests;
			productReference = A98C06E82B651BBC005AC0F3 /* practiceUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		A9F844002B842C8D00D35B12 /* PracticeWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A9F844162B842C8F00D35B12 /* Build configuration list for PBXNativeTarget "PracticeWidgetExtension" */;
			buildPhases = (
				A9F843FD2B842C8D00D35B12 /* Sources */,
				A9F843FE2B842C8D00D35B12 /* Frameworks */,
				A9F843FF2B842C8D00D35B12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PracticeWidgetExtension;
			packageProductDependencies = (
			);
			productName = PracticeWidgetExtension;
			productReference = A9F844012B842C8D00D35B12 /* PracticeWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A98C06C42B651BB9005AC0F3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					57F473F02BC697CC007E3315 = {
						CreatedOnToolsVersion = 15.2;
					};
					A98C06CB2B651BB9005AC0F3 = {
						CreatedOnToolsVersion = 15.2;
					};
					A98C06DD2B651BBC005AC0F3 = {
						CreatedOnToolsVersion = 15.2;
						TestTargetID = A98C06CB2B651BB9005AC0F3;
					};
					A98C06E72B651BBC005AC0F3 = {
						CreatedOnToolsVersion = 15.2;
						TestTargetID = A98C06CB2B651BB9005AC0F3;
					};
					A9F844002B842C8D00D35B12 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = A98C06C72B651BB9005AC0F3 /* Build configuration list for PBXProject "practice" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				ja,
				ko,
				de,
				ru,
				fr,
				ar,
				it,
			);
			mainGroup = A98C06C32B651BB9005AC0F3;
			packageReferences = (
				57E7CA1A2BA41F3C001BD416 /* XCRemoteSwiftPackageReference "HorizonCalendar" */,
				57E21D712BDB4FD300CC6072 /* XCRemoteSwiftPackageReference "ConfettiSwiftUI" */,
				C1FC68862DCB2D9D00ECCF34 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				3DC17AD88C3E419DB1E046B0 /* XCRemoteSwiftPackageReference "sentry-cocoa" */,
				C150FE1E2DD5FDB0004825A7 /* XCLocalSwiftPackageReference "../../lottie-ios-4.5.1" */,
			);
			productRefGroup = A98C06CD2B651BB9005AC0F3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A98C06CB2B651BB9005AC0F3 /* practice */,
				A98C06DD2B651BBC005AC0F3 /* practiceTests */,
				A98C06E72B651BBC005AC0F3 /* practiceUITests */,
				A9F844002B842C8D00D35B12 /* PracticeWidgetExtension */,
				57F473F02BC697CC007E3315 /* PracticeLiveActivityWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		57F473EF2BC697CC007E3315 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5731344F2BCE5DC900A2DFCE /* Localizable.xcstrings in Resources */,
				57F473FE2BC697CF007E3315 /* Assets.xcassets in Resources */,
				C1FC68832DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06CA2B651BB9005AC0F3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A92CF0D62B6FD6CF00D48AC2 /* Launch Screen.storyboard in Resources */,
				575BDBCA2D23CE3E0021C27C /* Product.storekit in Resources */,
				5761B5092BCE32C300E1D5A1 /* Localizable.xcstrings in Resources */,
				C10DEDBA2DF9B6D6007828F5 /* readme.md in Resources */,
				C122C0552DDACF110004E0A7 /* Nunito-Black.ttf in Resources */,
				C1ACA87E2E0444570075D350 /* glow.caf in Resources */,
				C1ACA87F2E0444570075D350 /* morning.caf in Resources */,
				C1ACA8802E0444570075D350 /* chime.mp3 in Resources */,
				C1ACA8812E0444570075D350 /* breeze.caf in Resources */,
				C1ACA8822E0444570075D350 /* rise.caf in Resources */,
				C1ACA8712E03DFDB0075D350 /* whistle.mp3 in Resources */,
				C1ACA8722E03DFDB0075D350 /* glow.mp3 in Resources */,
				C1ACA8732E03DFDB0075D350 /* rise.mp3 in Resources */,
				C1ACA8752E03DFDB0075D350 /* morning.mp3 in Resources */,
				C1ACA8782E03DFDB0075D350 /* breeze.mp3 in Resources */,
				C122C0562DDACF110004E0A7 /* Nunito-Medium.ttf in Resources */,
				C122C0572DDACF110004E0A7 /* Nunito-Regular.ttf in Resources */,
				C122C0582DDACF110004E0A7 /* Nunito-SemiBold.ttf in Resources */,
				C122C0592DDACF110004E0A7 /* Nunito-Bold.ttf in Resources */,
				A98C06D92B651BBC005AC0F3 /* Preview Assets.xcassets in Resources */,
				578920F42BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */,
				A98C06D62B651BBC005AC0F3 /* Assets.xcassets in Resources */,
				C1FC68822DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06DC2B651BBC005AC0F3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				578920F52BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */,
				C10DEDBC2DF9B6D6007828F5 /* readme.md in Resources */,
				C1FC68852DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06E62B651BBC005AC0F3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				578920F62BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */,
				C10DEDBD2DF9B6D6007828F5 /* readme.md in Resources */,
				C1FC68842DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9F843FF2B842C8D00D35B12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				578920F72BB410DC0061093B /* PrivacyInfo.xcprivacy in Resources */,
				C1FC68812DCB280A00ECCF34 /* GoogleService-Info.plist in Resources */,
				A9F844112B842C8F00D35B12 /* Assets.xcassets in Resources */,
				C10DEDBB2DF9B6D6007828F5 /* readme.md in Resources */,
				5731344E2BCE5DC800A2DFCE /* Localizable.xcstrings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		712872F68F2341B784F22A97 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script is responsible for uploading debug symbols and source context for Sentry.\nif which sentry-cli >/dev/null; then\nexport SENTRY_ORG=shawn-music\nexport SENTRY_PROJECT=apple-ios\nERROR=$(sentry-cli debug-files upload --include-sources \"$DWARF_DSYM_FOLDER_PATH\" 2>&1 >/dev/null)\nif [ ! $? -eq 0 ]; then\necho \"warning: sentry-cli - $ERROR\"\nfi\nelse\necho \"warning: sentry-cli not installed, download from https://github.com/getsentry/sentry-cli/releases\"\nfi\n";
		};
		C142722B2DCE3C5400EBB0B6 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist",
				"$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist",
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		57F473ED2BC697CC007E3315 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57F473F82BC697CD007E3315 /* PracticeLiveActivityWidgetLiveActivity.swift in Sources */,
				57F473FC2BC697CD007E3315 /* AppIntent.swift in Sources */,
				57F474062BC697EF007E3315 /* PracticeWidgetLiveActivity.swift in Sources */,
				57F474082BC6A4AB007E3315 /* CountupTimerLiveActivity.swift in Sources */,
				57F473FA2BC697CD007E3315 /* PracticeLiveActivityWidget.swift in Sources */,
				57F474072BC69838007E3315 /* CountdownTimerLiveActivity.swift in Sources */,
				57F473F62BC697CD007E3315 /* PracticeLiveActivityWidgetBundle.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06C82B651BB9005AC0F3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A9F843FC2B8384F900D35B12 /* ProgressYearCard.swift in Sources */,
				576A9D142BB54F6C002A5949 /* CountdownTimerLiveActivity.swift in Sources */,
				C190EAD12DB916710052CC4F /* DayCalendarPicker.swift in Sources */,
				57793C482C06D560007D80A8 /* TaskProgressData.swift in Sources */,
				C190EAE02DBBE9F30052CC4F /* SharePreviewFullScreenView.swift in Sources */,
				C190EACD2DB8CA000052CC4F /* WeekCalendarPicker.swift in Sources */,
				C122C04E2DDAC34B0004E0A7 /* StreakCardView.swift in Sources */,
				A98C070D2B653A7B005AC0F3 /* AchievementListView.swift in Sources */,
				C1256BDE2DDC4989006DC118 /* AnimatableNumber.swift in Sources */,
				A96344832B67E1B50058F857 /* TaskItem.swift in Sources */,
				C10DEDD02DFA7581007828F5 /* CollectionSortView.swift in Sources */,
				A98C06D22B651BB9005AC0F3 /* ContentView.swift in Sources */,
				A9D74C1C2B97702C00A92E3C /* TimerScrollSelect.swift in Sources */,
				C190EAD72DBB76010052CC4F /* MembershipManager.swift in Sources */,
				573F78872BFB2CF8004D7EBC /* StatisticsView.swift in Sources */,
				C190EAD42DB91C740052CC4F /* YearSummaryView.swift in Sources */,
				C190EAD52DB91C740052CC4F /* MonthSummaryView.swift in Sources */,
				C10DEDD42DFAA82A007828F5 /* ScrollableCoverView.swift in Sources */,
				57B4DE282BD10EBF000ABCC7 /* QRCode.swift in Sources */,
				C11452B32DB105E200E54806 /* RecordingItemView.swift in Sources */,
				A92CF0D42B6D352700D48AC2 /* DateUtils.swift in Sources */,
				57E7CA1C2BA425CD001BD416 /* TimeModifyView.swift in Sources */,
				57D41A6F2BC8DDF600EDBDA0 /* ArcProgressView.swift in Sources */,
				576A9D202BBBA203002A5949 /* CountingView.swift in Sources */,
				C190EAC32DB7BC650052CC4F /* StorageProvider.swift in Sources */,
				57B0053A2BA2F2610095DEAC /* PracticeTimeEditView.swift in Sources */,
				C10DEDD62DFAB4D0007828F5 /* ListCoverView.swift in Sources */,
				A98C07012B652232005AC0F3 /* CurrentListView.swift in Sources */,
				C11452A62DAFA90C00E54806 /* RecordingView.swift in Sources */,
				57980D2B2BBD12320048CB64 /* CountupTimerLiveActivity.swift in Sources */,
				577F78F82B9EFF59006C34CE /* UserPermission.swift in Sources */,
				C1AF57F52DFEBE7A0039A7CC /* AlbumGridView.swift in Sources */,
				A9F844502B8E315100D35B12 /* MonthCalendarProgressCardView.swift in Sources */,
				C10DEDCD2DFA58D4007828F5 /* TaskCardView.swift in Sources */,
				57D41A742BC90A8D00EDBDA0 /* PersonalConfig.swift in Sources */,
				57F473EC2BC63579007E3315 /* PracticeView.swift in Sources */,
				A98C07072B652E45005AC0F3 /* AddNewItemView.swift in Sources */,
				C1256BDA2DDB8668006DC118 /* ReminderTimeSelector.swift in Sources */,
				C10DEDCB2DFA5719007828F5 /* CollectionItemView.swift in Sources */,
				C10DEDC42DF9C3E0007828F5 /* CollectionListView.swift in Sources */,
				C11452AF2DB0EB5E00E54806 /* PlaybackSliderView.swift in Sources */,
				576A9D252BBC1B43002A5949 /* timeUtils.swift in Sources */,
				C1BF41692DAE8AA5009B7E6C /* TaskListManager.swift in Sources */,
				C11452A02DAF637F00E54806 /* RecordingManager.swift in Sources */,
				57B005352BA19DCB0095DEAC /* ColorUnit.swift in Sources */,
				C150FE1A2DD5F929004825A7 /* GoalTimeSelectView.swift in Sources */,
				C19EB7232E0978FE00CA1076 /* GalleryGridView.swift in Sources */,
				C119766B2DB5482C0079670B /* TaskManager.swift in Sources */,
				C11976652DB384FC0079670B /* TaskStatisticsNavigator.swift in Sources */,
				C190EAC52DB7E3B00052CC4F /* ImageManager.swift in Sources */,
				C10DEDBF2DF9C3BB007828F5 /* CollectionManager.swift in Sources */,
				57D41A722BC8FCAF00EDBDA0 /* SettingsView.swift in Sources */,
				5751A5E62BAAE637004932E4 /* CalendarViewUI.swift in Sources */,
				57793C462C06D54E007D80A8 /* TaskItemData.swift in Sources */,
				C119765F2DB165A30079670B /* CustomAlertView.swift in Sources */,
				C105312D2DE89FB200B3A029 /* AppGlobalStateManager.swift in Sources */,
				576A9D1D2BBA8151002A5949 /* CountDownTimePicker.swift in Sources */,
				C1AF57F32DFEB7940039A7CC /* Color+Extensions.swift in Sources */,
				C1256BD22DDB694B006DC118 /* TaskInfoCard.swift in Sources */,
				C190EADB2DBBE5320052CC4F /* ColorThemeManager.swift in Sources */,
				C14527B52DE5FFB20026ACA8 /* MatchGeo.swift in Sources */,
				C10DEDD82DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */,
				C11976672DB50DF20079670B /* NavigationManager.swift in Sources */,
				C1256BDC2DDBB84C006DC118 /* CustomSubscribeView.swift in Sources */,
				C14527B72DE600E80026ACA8 /* StreakCalendarView.swift in Sources */,
				A92CF0D12B6BDC2900D48AC2 /* TaskDetailView.swift in Sources */,
				C11452A82DAFB12300E54806 /* Waveform.swift in Sources */,
				C1256BE32DDD6F8A006DC118 /* ViewExtension.swift in Sources */,
				C190EAC12DB7B92A0052CC4F /* iCloudDocumentManager.swift in Sources */,
				A98C07102B654321005AC0F3 /* InfoCardView.swift in Sources */,
				C122C05B2DDAE8080004E0A7 /* TodayProgressView.swift in Sources */,
				C119766F2DB63BF10079670B /* WeekSummaryView.swift in Sources */,
				A93BF7B62BC11E230074FA2B /* InfoCardTimeView.swift in Sources */,
				C10DEDC82DF9C8B2007828F5 /* CreateCollectionView.swift in Sources */,
				C10DEDC92DF9C8B2007828F5 /* CollectionDetailView.swift in Sources */,
				C150FE172DD2D630004825A7 /* CloudImageView.swift in Sources */,
				C1ADA4D42DEEAD560030DAF8 /* VideoItemView.swift in Sources */,
				A98C06D02B651BB9005AC0F3 /* practiceApp.swift in Sources */,
				C114529E2DAEA21D00E54806 /* RatingManager.swift in Sources */,
				A92CF0CE2B6BC47000D48AC2 /* ProgressCardView.swift in Sources */,
				575BDBC62D23C7460021C27C /* SubscribeView.swift in Sources */,
				A97FFB272B72834500AED8F8 /* AddNewAchievementView.swift in Sources */,
				5754638F2BB17F6800F8837A /* InfoCardGalleryView.swift in Sources */,
				C11976582DB10C000079670B /* LaurelTimeView.swift in Sources */,
				57C939FA2BD2620900C391AB /* ShareImageFooter.swift in Sources */,
				C1ADA4D22DEEA6710030DAF8 /* VideoManager.swift in Sources */,
				C1AF57F12DFEB02C0039A7CC /* CollectionItem+Extensions.swift in Sources */,
				C190EAD92DBB76180052CC4F /* MembershipView.swift in Sources */,
				C1EAE0A72E0C3453009F3D00 /* RecordingEditView.swift in Sources */,
				576A9D232BBBA6BF002A5949 /* CountDownContainer.swift in Sources */,
				C1256BE12DDC7148006DC118 /* HapticManager.swift in Sources */,
				C1256BD62DDB8071006DC118 /* StepGuider.swift in Sources */,
				578197F92BB15EC10045F6C2 /* InfoCardCompactView.swift in Sources */,
				C11976692DB524DE0079670B /* ActionSheetWrapper.swift in Sources */,
				A9F8444E2B8E12DB00D35B12 /* CountDownView.swift in Sources */,
				C119766D2DB63BA50079670B /* DaySummaryView.swift in Sources */,
				57B005322BA153150095DEAC /* PracticeTime.swift in Sources */,
				C196D76F2DE028DC005F94E4 /* LoadingView.swift in Sources */,
				C1256BD42DDB731A006DC118 /* NotificationManager.swift in Sources */,
				C122C04B2DD987B60004E0A7 /* ChildShape.swift in Sources */,
				C11537D32DDD891300897938 /* CustomFontSize.swift in Sources */,
				C1BF416B2DAE8E81009B7E6C /* ConfigManager.swift in Sources */,
				C1ACA8662E03DBF20075D350 /* RingtoneSelector.swift in Sources */,
				C1256BD82DDB827D006DC118 /* OnboardingView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06DA2B651BBC005AC0F3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C119765B2DB10C000079670B /* LaurelTimeView.swift in Sources */,
				C122C0492DD987B60004E0A7 /* ChildShape.swift in Sources */,
				C10DEDD92DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A98C06E42B651BBC005AC0F3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C119765A2DB10C000079670B /* LaurelTimeView.swift in Sources */,
				C122C04A2DD987B60004E0A7 /* ChildShape.swift in Sources */,
				C10DEDDA2DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9F843FD2B842C8D00D35B12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C10DEDDB2DFAB8C0007828F5 /* LaurelTimeViewSmall.swift in Sources */,
				C122C04C2DD987B60004E0A7 /* ChildShape.swift in Sources */,
				A9F8440D2B842C8D00D35B12 /* PracticeWidget.swift in Sources */,
				57B005382BA2E7980095DEAC /* PracticeTime.swift in Sources */,
				57980D2D2BBD33EE0048CB64 /* timeUtils.swift in Sources */,
				576B36E02C0DBC3500462961 /* DailySummaryWidgetView.swift in Sources */,
				57B005362BA1A0350095DEAC /* ColorUnit.swift in Sources */,
				A9F8442A2B84D51900D35B12 /* TaskItem.swift in Sources */,
				A9F8440F2B842C8D00D35B12 /* YearProcessWidgetIntent.swift in Sources */,
				576B36E22C0DC4BE00462961 /* TaskProgressData.swift in Sources */,
				57D41A752BC90CA200EDBDA0 /* PersonalConfig.swift in Sources */,
				57B005372BA1AEBD0095DEAC /* DateUtils.swift in Sources */,
				576B36DF2C0DBC1B00462961 /* DailySummaryWidget.swift in Sources */,
				A9F8442B2B84D59C00D35B12 /* ProgressYearCard.swift in Sources */,
				A9F844092B842C8D00D35B12 /* PracticeWidgetBundle.swift in Sources */,
				C11976592DB10C000079670B /* LaurelTimeView.swift in Sources */,
				A9F8442E2B84D95900D35B12 /* YearProcessWidget.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		57F474012BC697CF007E3315 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 57F473F02BC697CC007E3315 /* PracticeLiveActivityWidgetExtension */;
			targetProxy = 57F474002BC697CF007E3315 /* PBXContainerItemProxy */;
		};
		A93BF7B82BC151AF0074FA2B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A9F844002B842C8D00D35B12 /* PracticeWidgetExtension */;
			targetProxy = A93BF7B72BC151AF0074FA2B /* PBXContainerItemProxy */;
		};
		A98C06E02B651BBC005AC0F3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A98C06CB2B651BB9005AC0F3 /* practice */;
			targetProxy = A98C06DF2B651BBC005AC0F3 /* PBXContainerItemProxy */;
		};
		A98C06EA2B651BBC005AC0F3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A98C06CB2B651BB9005AC0F3 /* practice */;
			targetProxy = A98C06E92B651BBC005AC0F3 /* PBXContainerItemProxy */;
		};
		A9F844142B842C8F00D35B12 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A9F844002B842C8D00D35B12 /* PracticeWidgetExtension */;
			targetProxy = A9F844132B842C8F00D35B12 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		57F474042BC697CF007E3315 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PracticeLiveActivityWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PracticeLiveActivityWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice.PracticeLiveActivityWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		57F474052BC697CF007E3315 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PracticeLiveActivityWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PracticeLiveActivityWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice.PracticeLiveActivityWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A98C06F02B651BBC005AC0F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A98C06F12B651BBC005AC0F3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A98C06F32B651BBC005AC0F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = practice/practice.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"practice/Preview Content\"";
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = practice/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Practice;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.music";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Need Microphone to record practice";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "We need access to save your achievement image.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "";
				INFOPLIST_KEY_NSSupportsLiveActivities = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.6.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A98C06F42B651BBC005AC0F3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = practice/practice.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"practice/Preview Content\"";
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = practice/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Practice;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.music";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Need Microphone to record practice";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "We need access to save your achievement image.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "";
				INFOPLIST_KEY_NSSupportsLiveActivities = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.6.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A98C06F62B651BBC005AC0F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practiceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/practice.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/practice";
			};
			name = Debug;
		};
		A98C06F72B651BBC005AC0F3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practiceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/practice.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/practice";
			};
			name = Release;
		};
		A98C06F92B651BBC005AC0F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practiceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = practice;
			};
			name = Debug;
		};
		A98C06FA2B651BBC005AC0F3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practiceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = practice;
			};
			name = Release;
		};
		A9F844172B842C8F00D35B12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = PracticeWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PracticeWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PracticeWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice.PracticeWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A9F844182B842C8F00D35B12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = PracticeWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 27B4FXC9UK;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PracticeWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PracticeWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.shawnchenxmu.practice.PracticeWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		57F474032BC697CF007E3315 /* Build configuration list for PBXNativeTarget "PracticeLiveActivityWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				57F474042BC697CF007E3315 /* Debug */,
				57F474052BC697CF007E3315 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A98C06C72B651BB9005AC0F3 /* Build configuration list for PBXProject "practice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A98C06F02B651BBC005AC0F3 /* Debug */,
				A98C06F12B651BBC005AC0F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A98C06F22B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A98C06F32B651BBC005AC0F3 /* Debug */,
				A98C06F42B651BBC005AC0F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A98C06F52B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practiceTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A98C06F62B651BBC005AC0F3 /* Debug */,
				A98C06F72B651BBC005AC0F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A98C06F82B651BBC005AC0F3 /* Build configuration list for PBXNativeTarget "practiceUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A98C06F92B651BBC005AC0F3 /* Debug */,
				A98C06FA2B651BBC005AC0F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A9F844162B842C8F00D35B12 /* Build configuration list for PBXNativeTarget "PracticeWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A9F844172B842C8F00D35B12 /* Debug */,
				A9F844182B842C8F00D35B12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		C150FE1E2DD5FDB0004825A7 /* XCLocalSwiftPackageReference "../../lottie-ios-4.5.1" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "../../lottie-ios-4.5.1";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		3DC17AD88C3E419DB1E046B0 /* XCRemoteSwiftPackageReference "sentry-cocoa" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/getsentry/sentry-cocoa/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		57E21D712BDB4FD300CC6072 /* XCRemoteSwiftPackageReference "ConfettiSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/simibac/ConfettiSwiftUI.git";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		57E7CA1A2BA41F3C001BD416 /* XCRemoteSwiftPackageReference "HorizonCalendar" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/HorizonCalendar";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		C1FC68862DCB2D9D00ECCF34 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.12.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		570D6F812BA4284700BA0C0A /* HorizonCalendar */ = {
			isa = XCSwiftPackageProductDependency;
			package = 57E7CA1A2BA41F3C001BD416 /* XCRemoteSwiftPackageReference "HorizonCalendar" */;
			productName = HorizonCalendar;
		};
		57E21D722BDB4FD300CC6072 /* ConfettiSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 57E21D712BDB4FD300CC6072 /* XCRemoteSwiftPackageReference "ConfettiSwiftUI" */;
			productName = ConfettiSwiftUI;
		};
		C14272292DCE3B9300EBB0B6 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			productName = FirebaseCrashlytics;
		};
		C142722C2DCE3F1800EBB0B6 /* FirebasePerformance */ = {
			isa = XCSwiftPackageProductDependency;
			productName = FirebasePerformance;
		};
		C150FE1F2DD5FDB0004825A7 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			productName = Lottie;
		};
		C1FC68872DCB2D9D00ECCF34 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1FC68862DCB2D9D00ECCF34 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		EA2A596CC2394E5BAB60C153 /* Sentry */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3DC17AD88C3E419DB1E046B0 /* XCRemoteSwiftPackageReference "sentry-cocoa" */;
			productName = Sentry;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A98C06C42B651BB9005AC0F3 /* Project object */;
}

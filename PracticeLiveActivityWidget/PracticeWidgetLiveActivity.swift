//
//  PracticeWidgetLiveActivity.swift
//  PracticeWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/20.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct PracticeWidgetAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

struct PracticeWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: PracticeWidgetAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

extension PracticeWidgetAttributes {
    fileprivate static var preview: PracticeWidgetAttributes {
        PracticeWidgetAttributes(name: "World")
    }
}

extension PracticeWidgetAttributes.ContentState {
    fileprivate static var smiley: PracticeWidgetAttributes.ContentState {
        PracticeWidgetAttributes.ContentState(emoji: "😀")
     }
     
     fileprivate static var starEyes: PracticeWidgetAttributes.ContentState {
         PracticeWidgetAttributes.ContentState(emoji: "🤩")
     }
}

#Preview("Notification", as: .content, using: PracticeWidgetAttributes.preview) {
   PracticeWidgetLiveActivity()
} contentStates: {
    PracticeWidgetAttributes.ContentState.smiley
    PracticeWidgetAttributes.ContentState.starEyes
}

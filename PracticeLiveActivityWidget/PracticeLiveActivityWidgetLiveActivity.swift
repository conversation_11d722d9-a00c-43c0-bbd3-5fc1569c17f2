//
//  PracticeLiveActivityWidgetLiveActivity.swift
//  PracticeLiveActivityWidget
//
//  Created by 陈昇 on 4/10/24.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct PracticeLiveActivityWidgetAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

struct PracticeLiveActivityWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: PracticeLiveActivityWidgetAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

extension PracticeLiveActivityWidgetAttributes {
    fileprivate static var preview: PracticeLiveActivityWidgetAttributes {
        PracticeLiveActivityWidgetAttributes(name: "World")
    }
}

extension PracticeLiveActivityWidgetAttributes.ContentState {
    fileprivate static var smiley: PracticeLiveActivityWidgetAttributes.ContentState {
        PracticeLiveActivityWidgetAttributes.ContentState(emoji: "😀")
     }
     
     fileprivate static var starEyes: PracticeLiveActivityWidgetAttributes.ContentState {
         PracticeLiveActivityWidgetAttributes.ContentState(emoji: "🤩")
     }
}

#Preview("Notification", as: .content, using: PracticeLiveActivityWidgetAttributes.preview) {
   PracticeLiveActivityWidgetLiveActivity()
} contentStates: {
    PracticeLiveActivityWidgetAttributes.ContentState.smiley
    PracticeLiveActivityWidgetAttributes.ContentState.starEyes
}
